apply plugin: 'com.android.application'

android {
    compileSdkVersion 30
    buildToolsVersion "30.0.2"

    defaultConfig {
        applicationId "com.toocms.wago"
        minSdkVersion 21
        targetSdkVersion 30
        versionCode 20220207
        versionName "1.1.10"
        multiDexEnabled true
        manifestPlaceholders = [qqappid: "1105240612"]
        buildFeatures { dataBinding = true }
    }

    android.applicationVariants.all { variant ->
        variant.outputs.all {
            outputFileName = "wago-${defaultConfig.versionName}.apk"
        }
    }

    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8    // Java1.8
        sourceCompatibility JavaVersion.VERSION_1_8
    }


    signingConfigs {
        release {
            storeFile file('F://android.keystore')
            storePassword 'bay011645'
            keyAlias 'bay011645'
            keyPassword 'bay011645'
        }

        debug {
            storeFile file('F://android.keystore')
            storePassword "bay011645"
            keyAlias "bay011645"
            keyPassword "bay011645"
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }

        debug {
            minifyEnabled false
            signingConfig signingConfigs.release
        }
    }


    //添加aar包依赖，必要、固定
    repositories {
        flatDir {
            dirs 'libs'
        }
    }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }
}

dependencies {
    def tab6_version = "6.0.7"
    implementation fileTree(dir: "libs", include: ["*.jar"])
    implementation "com.gitee.toocms-android:tab6:${tab6_version}" // 核心必须依赖
    implementation "com.gitee.toocms-android.tab6:tab-expand:${tab6_version}" // 扩展包
    implementation "com.gitee.toocms-android.tab6:tab-share:${tab6_version}"   // 分享包
    implementation "com.gitee.toocms-android.tab6:tab-push:${tab6_version}"   // 推送包
    implementation 'cn.jzvd:jiaozivideoplayer:7.7.0'
}