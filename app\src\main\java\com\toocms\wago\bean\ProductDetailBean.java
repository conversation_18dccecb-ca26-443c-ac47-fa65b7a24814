package com.toocms.wago.bean;

import java.util.List;

/**
 * Author：Zero
 * Date：2021/6/18
 */
public class ProductDetailBean {

    public ProductBean product;
    public List<BannerListBean> bannerList;
    public List<ListBean> list;

    public static class ProductBean {
        public String productId;
        public String productType;
        public String categoryId1;
        public String categoryId2;
        public String categoryId3;
        public String totalTitle;
        public String subhead;
        public String color;
        public String thumbnailUrl;
        public List<BannerListBean> bannerList;
        public int iscollect;
        public String pdfUrl;

        public static class BannerListBean {
            public String bannerId;
            public String productId;
            public String bannerUrl;
            public String createTime;
            public int status;
            public int isDelete;
            public String updateTime;
            public boolean isImg;
            public String fengmianImgUrl;

            @Override
            public String toString() {
                return "BannerListBean{" +
                        "bannerId='" + bannerId + '\'' +
                        ", productId='" + productId + '\'' +
                        ", bannerUrl='" + bannerUrl + '\'' +
                        ", createTime='" + createTime + '\'' +
                        ", status=" + status +
                        ", isDelete=" + isDelete +
                        ", updateTime='" + updateTime + '\'' +
                        ", isImg=" + isImg +
                        ", fengmianImgUrl='" + fengmianImgUrl + '\'' +
                        '}';
            }
        }
    }

    public static class BannerListBean {
        public String bannerId;
        public String productId;
        public String bannerUrl;
        public String createTime;
        public int status;
        public int isDelete;
        public String updateTime;
        public boolean isImg;
        public String fengmianImgUrl;
    }

    public static class ListBean {
        public String gropuName;
        public List<ListBean._ListBean> list;
        public List<ListBean.ValueListBean> valueList;

        public static class _ListBean {
            public String gropuName;
            public Object list;
            public List<ListBean._ListBean.ValueListBean> valueList;

            public static class ValueListBean {
                public String specName;
                public String value;
            }
        }

        public static class ValueListBean {
            public String specName;
            public String value;
        }
    }
}
