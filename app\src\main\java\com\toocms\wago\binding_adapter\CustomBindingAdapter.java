package com.toocms.wago.binding_adapter;

import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.webkit.WebSettings;
import android.widget.ImageView;

import androidx.databinding.BindingAdapter;

import com.google.android.material.tabs.TabLayout;
import com.qmuiteam.qmui.widget.QMUIFloatLayout;
import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;
import com.toocms.tab.imageload.ImageLoader;

import java.io.File;
import java.util.List;

public class CustomBindingAdapter {

    @BindingAdapter({"viewSelectStatus"})
    public static void selectStatus(View view, boolean viewSelectStatus) {
        if (null == view) return;
        view.setSelected(viewSelectStatus);
    }

    @BindingAdapter({"loadHtml"})
    public static void loadHtml(QMUIWebView qmuiWebView, String htmlOrText) {
        qmuiWebView.setWebViewClient(new QMUIWebViewClient(true, false));
        WebSettings settings = qmuiWebView.getSettings();
        settings.setDatabaseEnabled(true);
        settings.setDisplayZoomControls(false);
        settings.setJavaScriptEnabled(true);
        settings.setSupportZoom(false);
        qmuiWebView.loadDataWithBaseURL(null, htmlOrText, "text/html", "UTF-8", null);
    }

    @BindingAdapter(value = {"imagePath", "placeholderRes"}, requireAll = false)
    public static void bitmap(ImageView imageView, String imagePath, int placeholderRes) {
        ImageLoader.loadFile2Image(new File(imagePath), imageView, placeholderRes);
    }

    @BindingAdapter(value = {"tabNames", "tags", "onTabSelectedListener"}, requireAll = false)
    public static void tabLayoutTabs(TabLayout tabLayout, List<String> tabNames, List<String> tags, TabLayout.OnTabSelectedListener onTabSelectedListener) {
        for (int i = 0; i < tabNames.size(); i++) {
            TabLayout.Tab tab = tabLayout.newTab().setText(tabNames.get(i));
            if (i < tags.size()) {
                tab.setTag(tags.get(i));
            }
            tabLayout.addTab(tab);
        }
        if (null != onTabSelectedListener)
            tabLayout.addOnTabSelectedListener(onTabSelectedListener);
    }

    @BindingAdapter({"floatChildren"})
    public static void floatLayoutChildren(QMUIFloatLayout floatLayout,List<View> views){
        for (View view :views) {
            ViewParent parent = view.getParent();
            if(null!=parent){
                ((ViewGroup)parent).removeView(view);
            }
            floatLayout.addView(view);
        }
    }

}
