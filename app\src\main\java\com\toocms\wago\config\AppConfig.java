package com.toocms.wago.config;

import android.app.Application;
import android.content.Context;

import com.toocms.tab.configs.IAppConfig;
import com.toocms.tab.push.TabPush;
import com.toocms.tab.share.TabShare;
import com.toocms.wago.ui.mine.system_message.SystemMessageFgt;
import com.umeng.message.UmengNotificationClickHandler;
import com.umeng.message.entity.UMessage;

import rxhttp.wrapper.param.Method;
import rxhttp.wrapper.param.Param;

/**
 * App配置
 */
public class AppConfig implements IAppConfig {

    @Override
    public String getBaseUrl() {
        return "https://www.app.hemajia.net/wagoapp/wagoapp/product/";
    }

    @Override
    public String getUpdateUrl() {
        return "System/checkVersion";
    }

    @Override
    public String getUmengAppkey() {
        return "60d311e88a102159db78816a";
    }

    @Override
    public String getUmengPushSecret() {
        return "97fcfdc6bf395920ba875b7ac42a09dd";
    }

    @Override
    public void initJarForWeApplication(Application application) {
        TabPush.getInstance().register(new UmengNotificationClickHandler() {

            @Override
            public void dealWithCustomAction(Context context, UMessage uMessage) {
                super.dealWithCustomAction(context, uMessage);
                TabPush.getInstance().startFragment(context, SystemMessageFgt.class, null);
            }
        });
        TabShare.registerWX(application, "wxdb41e09d14a28076", "1c0da888b05b099a30f6b43d79d3965d");
    }

    @Override
    public Param<?> setOnParamAssembly(Param<?> param) {
        Method method = param.getMethod();
        if (method.isPost()) param.addHeader("Content-Type", "application/json");
        return param;
    }
}
