package com.toocms.wago.config;

/**
 * 全局常量字段
 *
 * <AUTHOR>
 * @date 2016/1/22 9:46
 */
public class Constants {

    /**
     * 接口返回的message字段，定义成常量以免以后接口出现改动
     */
    public static final String MESSAGE = "message";
    /**
     * 消息状态
     */
    public static final String MESSAGE_STATUS_READ = "1";
    public static final String MESSAGE_STATUS_NO_READ = "0";
    /**
     * 图片默认的上限
     */
    public static final int DEFAULT_MAX_IMAGE_NUMBER = 4;

    /**
     * 默认金额
     */
    public static final String DEFAULT_MONEY = "0.00";

    /**
     * 轮播图旋转时间
     */
    public static final int BANNER_TURNING_TIME = 5000; //毫秒级

    /**
     * 验证码倒计时持续时间
     */
    public static final long VERIFY_CODE_COUNT_DOWN_DURATION = 60 * 1000;

    /**
     * 定位结果
     */

    public static final String LOCATION_RESULT = "locationResult";

    // 权限请求标识码
    public static final int PERMISSIONS_CALL_PHONE = 0x110; // 打电话权限请求代码
    public static final int PERMISSIONS_RECORD = 0x140; // 打电话权限请求代码
    public static final int PERMISSIONS_WRITE_EXTERNAL_STORAGE = 0x120; // SD卡读写权限请求代码
    public static final int PERMISSIONS_AMAP_LOCATION = 0x130; // 高德地图定位相关权限

    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

    public static final String DECOLLATOR_DEFAULT = ","; //默认分割符

    /**
     * 历史记录KEY
     */
    public static final String SEARCH_HISTORY_KEY = "searchHistory";

    /**
     * 验证验证码 模板标识
     */
    public static final String UNIQUE_CODE_REGISTER = "register"; //注册
    public static final String UNIQUE_CODE_LOGIN = "login"; //登录
    public static final String UNIQUE_CODE_FIND_PASSWORD = "retPassword"; //找回密码
    //    public static final String UNIQUE_CODE_MODIFY_ACCOUNT = "bindNewAccount"; //修改手机账号时原手机号获取验证码
//    public static final String UNIQUE_CODE_SET_PHONE = "bindNewAccount"; //修改手机账号时新手机号获取验证码
    public static final String UNIQUE_CODE_SET_PAYMENT_PASSWORD = "set_pay_pass"; //设置支付密码
    public static final String UNIQUE_CODE_MODIFY_ACCOUNT = "modify_account"; //修改手机号

    /**
     * RecyclerView type code
     */
    public static final String RECYCLER_VIEW_ITEM_TYPE_ONE = "0x0001";
    public static final String RECYCLER_VIEW_ITEM_TYPE_TWO = "0x0010";
    public static final String RECYCLER_VIEW_ITEM_TYPE_THREE = "0x0100";
    public static final String RECYCLER_VIEW_ITEM_TYPE_FOUR = "0x1000";
    public static final String RECYCLER_VIEW_ITEM_TYPE_FIVE = "0x0011";
    public static final String RECYCLER_VIEW_ITEM_TYPE_SIX = "0x0101";
    public static final String RECYCLER_VIEW_ITEM_TYPE_SEVEN = "0x0110";
    public static final String RECYCLER_VIEW_ITEM_TYPE_EIGHT = "0x1001";
    public static final String RECYCLER_VIEW_ITEM_TYPE_NINE = "0x1010";

    /**
     * Activity请求码
     */
    public static final int ACTIVITY_REQUEST_CODE_ONE = 0x0001;
    public static final int ACTIVITY_REQUEST_CODE_TWO = 0x0010;
    public static final int ACTIVITY_REQUEST_CODE_THREE = 0x0100;
    public static final int ACTIVITY_REQUEST_CODE_FOUR = 0x1000;
    public static final int ACTIVITY_REQUEST_CODE_FIVE = 0x0011;
    public static final int ACTIVITY_REQUEST_CODE_SIX = 0x0101;
    public static final int ACTIVITY_REQUEST_CODE_SEVEN = 0x0110;
    public static final int ACTIVITY_REQUEST_CODE_EIGHT = 0x1001;
    public static final int ACTIVITY_REQUEST_CODE_NINE = 0x1010;


    /**
     * Messenger token
     */
    public static final String MESSENGER_TOKEN_TOP_CLASSIFY_CLICK = "topClassifyClick"; //顶级分类点击
    public static final String MESSENGER_TOKEN_SECONDARY_CLASSIFY_CLICK = "secondaryClassifyClick"; //顶级分类点击
    public static final String MESSENGER_TOKEN_EDIT_STATUS = "editStatus"; //编辑状态
    public static final String MESSENGER_TOKEN_FINISH_LOGIN = "MESSENGER_TOKEN_FINISH_LOGIN"; // 结束登录页

    public static final String KEY_IMAGES = "images"; //图片列表
    public static final String KEY_POSITION = "position"; //位置

    /**
     * Poi搜索类型
     * 010000 汽车服务
     * 020000 汽车销售
     * 030000 汽车维修
     * 040000 摩托车服务
     * 050000 餐饮服务
     * 060000 购物服务
     * 070000 生活服务
     * 080000 体育休闲服务
     * 090000 医疗保健服务
     * 100000 住宿服务
     * 110000 风景名胜
     * 120000 商务住宅
     * 130000 政府机构及社会团体
     * 140000 科教文化服务
     * 150000 交通设施服务
     * 160000 金融保险服务
     * 170000 公司企业
     * 180000 道路附属设施
     * 190000 地名地址信息
     * 200000 公共设施
     * 220000 事件活动
     * 990000 通行设施
     */
    public static final String POI_CTGR = "200000|990000|190000|170000|160000|150000|140000|130000|120000|110000|100000|090000|080000|070000|060000|050000|020000|010000";

    /**
     * 将带[]的字符串转换成String[]
     *
     * @param str
     * @return
     */
    public static String[] parseArray(String str) {
        return str.replaceAll("\\[([^\\]]*)\\]", "$1").replaceAll("\"([^\"]*)\"", "$1").split(",");
    }

    // 详情展示类型
    public static final String DETAIL_TYPE_PRODUCT = "DETAIL_TYPE_PRODUCT";     // 产品
    public static final String DETAIL_TYPE_PRODUCT_PLANS = "DETAIL_TYPE_PRODUCT_PLANS";     // 解决方案
    public static final String DETAIL_TYPE_MATERIAL_FILE = "DETAIL_TYPE_MATERIAL_FILE";     // 手册文件
    public static final String DETAIL_TYPE_CERTIFICATE_FILE = "DETAIL_TYPE_CERTIFICATE_FILE";     // 证书文件
}
