package com.toocms.wago.dec;

import android.content.Context;
import android.util.TypedValue;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class DpLinearLayoutDecoration extends LinearLayoutDecoration {
    private Context context;

    public DpLinearLayoutDecoration(Context context) {
        this(context, 1);
    }

    public DpLinearLayoutDecoration(Context context, int dividerHeight) {
        this(context, dividerHeight, 0);
    }

    public DpLinearLayoutDecoration(Context context, int dividerHeight, int startItem) {
        super((int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,dividerHeight,context.getResources().getDisplayMetrics()), startItem);
        this.context = context;
    }

    @Override
    public void setDividerHeight(int dividerHeight) {
        super.setDividerHeight(dp2px(dividerHeight));
    }

    @Override
    public void setLeftOffset(int leftOffset) {
        super.setLeftOffset(dp2px( leftOffset));
    }

    @Override
    public void setRightOffset(int rightOffset) {
        super.setRightOffset(dp2px(rightOffset));
    }

    @Override
    public void setWidthOffset(int widthOffset) {
        super.setWidthOffset(dp2px(widthOffset));
    }

    @Override
    public void setButtcockLineWidth(int buttcockLineWidth) {
        super.setButtcockLineWidth(dp2px(buttcockLineWidth));
    }

    @Override
    public void setHeadLineWidth(int headLineWidth) {
        super.setHeadLineWidth(dp2px(headLineWidth));
    }
    private int dp2px(float dpValue){
//        return (int) TypedValue.applyDimension(TypedValue.COMPLEX_UNIT_DIP,dpValue,this.context.getResources().getDisplayMetrics());
        return AutoSizeUtils.dp2px(context,dpValue);
    }
}
