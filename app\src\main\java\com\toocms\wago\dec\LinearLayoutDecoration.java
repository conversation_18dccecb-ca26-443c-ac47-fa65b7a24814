package com.toocms.wago.dec;

import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.view.View;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

/**
 * Vertical方向的RecyclerView画线
 */
public class LinearLayoutDecoration extends RecyclerView.ItemDecoration {
    private Paint mPaint;
    private int orientation; //方向
    private int startItem = 0; //从第几条Item开始画
    private int dividerColor = 0xFFF2F2F2; //分割线的颜色
    private int dividerHeight = 0; //线的高度
    private int widthOffset = 0; //左右同时偏移的宽度
    private int leftOffset = 0; //左偏移的宽度
    private int rightOffset = 0;  //右偏移的宽度
    private boolean isButtcockLine = false; //在最后一个条目的下面是否画线
    private int buttcockLineWidth = -1; //尾线的宽,默认为-1,即同其他的线宽相同
    private int headLineWidth = -1; //第一条线的宽,默认为-1,即同其他的线宽相同

    public LinearLayoutDecoration() {
        init(0, 0);
    }

    public LinearLayoutDecoration(int dividerHeight, int startItem) {
        init(dividerHeight, startItem);
    }

    private void init(int dividerHeight, int startItem) {
        mPaint = new Paint();
        this.startItem = startItem;
        this.dividerHeight = dividerHeight;
        mPaint.setColor(this.dividerColor);
    }

    @Override
    public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (!(layoutManager instanceof LinearLayoutManager)) {
            throw new IllegalArgumentException("暂不支持该类型的 LayoutManager");
        }
        this.orientation = ((LinearLayoutManager) layoutManager).getOrientation();
        if (LinearLayoutManager.VERTICAL != this.orientation && LinearLayoutManager.HORIZONTAL != this.orientation) {
            throw new IllegalArgumentException("暂不支持该类型的Orientation");
        }
        if (LinearLayoutManager.VERTICAL == this.orientation) {
            offsetsVertical(outRect, view, parent, state);
        } else {
            offsetsHorizontal(outRect, view, parent, state);
        }
    }

    private void offsetsHorizontal(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view);
        int count = parent.getAdapter().getItemCount();
        outRect.top = widthOffset;
        outRect.bottom = widthOffset;
        if (startItem <= position) {
            if (startItem == position) {
                outRect.left = -1 == headLineWidth ? dividerHeight : headLineWidth;
            } else {
                outRect.left = dividerHeight;
            }
        }
        if (isButtcockLine && 1 == (count - position)) {
            outRect.right = -1 == buttcockLineWidth ? dividerHeight : buttcockLineWidth;
        }
    }

    private void offsetsVertical(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        if(View.GONE==view.getVisibility()){
            return;
        }
        int position = parent.getChildAdapterPosition(view);
        int count = parent.getAdapter().getItemCount();
        outRect.left = widthOffset;
        outRect.right = widthOffset;
        if (startItem <= position) {
            if (startItem == position) {
                outRect.top = -1 == headLineWidth ? dividerHeight : headLineWidth;
            } else {
                outRect.top = dividerHeight;
            }
        }
        if (isButtcockLine && 1 == (count - position)) {
            outRect.bottom = -1 == buttcockLineWidth ? dividerHeight : buttcockLineWidth;
        }
    }

    @Override
    public void onDraw(Canvas c, RecyclerView parent, RecyclerView.State state) {
        super.onDraw(c, parent, state);

        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (!(layoutManager instanceof LinearLayoutManager)) {
            throw new IllegalArgumentException("暂不支持该类型的 LayoutManager");
        }
        this.orientation = ((LinearLayoutManager) layoutManager).getOrientation();
        if (LinearLayoutManager.VERTICAL != this.orientation && LinearLayoutManager.HORIZONTAL != this.orientation) {
            throw new IllegalArgumentException("暂不支持该类型的Orientation");
        }
        if (LinearLayoutManager.VERTICAL == this.orientation) {
            drawVertical(c, parent, state);
        } else {
            drawHorizontal(c, parent, state);
        }


    }

    private void drawHorizontal(Canvas c, RecyclerView parent, RecyclerView.State state) {
        int count = parent.getChildCount();
        int itemCount = parent.getAdapter().getItemCount();
        for (int i = 0; i < count; i++) {
            View child = parent.getChildAt(i);
            if(View.GONE==child.getVisibility()){
                continue;
            }
            int position = parent.getChildAdapterPosition(child);
            int top = child.getTop() + leftOffset;
            int bottom = child.getBottom() - rightOffset;
            if (startItem <= position) {
                int right = child.getLeft();
                int left = right - dividerHeight;
                if (startItem == position) {
                    left = -1 == headLineWidth ? left : right - headLineWidth;
                }
                c.drawRect(left, top, right, bottom, mPaint);
            }
            if (isButtcockLine && 1 == (itemCount - position)) {
                int left = child.getRight();
                int right = left + (-1 == buttcockLineWidth ? dividerHeight : buttcockLineWidth);
                c.drawRect(left, top, right, bottom, mPaint);
            }
        }

    }

    private void drawVertical(Canvas c, RecyclerView parent, RecyclerView.State state) {
        int count = parent.getChildCount();
        int itemCount = parent.getAdapter().getItemCount();
        for (int i = 0; i < count; i++) {
            View child = parent.getChildAt(i);
            //如果Child的visibility属性为View.GONE则不绘制线
            if(View.GONE==child.getVisibility()){
                continue;
            }
            int position = parent.getChildAdapterPosition(child);
            int left = child.getLeft() + leftOffset;
            int right = child.getRight() - rightOffset;
            if (startItem <= position) {
                int bottom = child.getTop();
                int top = bottom - dividerHeight;
                if (startItem == position) {
                    top = -1 == headLineWidth ? top : bottom - headLineWidth;
                }
                c.drawRect(left, top, right, bottom, mPaint);
            }
            if (isButtcockLine && 1 == (itemCount - position)) {
                int top = child.getBottom();
                int bottom = top + (-1 == buttcockLineWidth ? dividerHeight : buttcockLineWidth);
                c.drawRect(left, top, right, bottom, mPaint);
            }
        }
    }

    public void setStartItem(int startItem) {
        this.startItem = startItem;
    }

    public void setWidthOffset(int widthOffset) {
        this.widthOffset = widthOffset;
    }

    public void setDividerColor(int dividerColor) {
        mPaint.setColor(dividerColor);
    }

    public void setDividerHeight(int dividerHeight) {
        this.dividerHeight = dividerHeight;
    }

    public void setLeftOffset(int leftOffset) {
        this.leftOffset = leftOffset;
    }

    public void setRightOffset(int rightOffset) {
        this.rightOffset = rightOffset;
    }

    public void setButtcockLine(boolean isButtcockLine) {
        this.isButtcockLine = isButtcockLine;
    }

    public void setButtcockLineWidth(int buttcockLineWidth) {
        this.buttcockLineWidth = buttcockLineWidth;
    }

    public void setHeadLineWidth(int headLineWidth) {
        this.headLineWidth = headLineWidth;
    }
}
