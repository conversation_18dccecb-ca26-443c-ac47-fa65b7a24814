package com.toocms.wago.dec.group;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.ColorInt;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.toocms.wago.dec.group.bean.BaseGroupBean;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class GroupDecoration<T extends BaseGroupBean> extends RecyclerView.ItemDecoration {
    private final Context mContext; //上下文
    private final Helper mHelper;
    private final SparseArray<T> mGroups = new SparseArray<>(); //分组集合
    private final SparseArray<View> mGroupsView = new SparseArray<>(); //测量后的分组集合

    private Paint mDividerPaint;

    private boolean isPasteTop = false; //是否吸顶

    private int dividerHeight = 0; //分割线高度(单位:dp)
    @ColorInt
    private int defaultDividerColor = 0xFFF2F2F2; //默认分割线颜色

    public GroupDecoration(Context context, Helper<T> helper) {
        this.mContext = context;
        mHelper = helper;
        initPaint();
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) throws IllegalStateException {
        super.getItemOffsets(outRect, view, parent, state);
        if (!(parent.getLayoutManager() instanceof LinearLayoutManager)) {
            throw new IllegalStateException("暂不支持" + parent.getLayoutManager().getClass().getName());
        }
        int position = parent.getChildAdapterPosition(view);
        T group = mGroups.get(position);
        if (null == group) {
            outRect.top = dp2px(dividerHeight);
        } else {
            View groupView = getGroupView(group.getGroupLayoutId(), parent);
            outRect.top = measureView(groupView, parent).getHeight();
        }
    }

    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(c, parent, state);
        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            int position = parent.getChildAdapterPosition(child);
            T groupBean = mGroups.get(position);

            if (null == groupBean) {
                int left = child.getLeft();
                int right = child.getRight();
                int bottom = child.getTop();
                int top = bottom - dp2px(dividerHeight);
                c.drawRect(left, top, right, bottom, mDividerPaint);
            } else {
                c.save();
                View groupView = getGroupView(groupBean.getGroupLayoutId(), parent);
                measureView(groupView, parent);
                mHelper.callback(groupView, groupBean);
                c.translate(0, child.getTop() - groupView.getMeasuredHeight());
                groupView.draw(c);
                c.restore();
            }
        }
    }

    @Override
    public void onDrawOver(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDrawOver(c, parent, state);
        if (!isPasteTop) {
            return;
        }
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (!(layoutManager instanceof LinearLayoutManager)) {
            return;
        }
        LinearLayoutManager linearLayoutManager = (LinearLayoutManager) layoutManager;
        int firstVisibleItemPosition = linearLayoutManager.findFirstVisibleItemPosition(); //获取第一个显示的Item
        int firstCompletelyVisibleItemPosition = linearLayoutManager.findFirstCompletelyVisibleItemPosition(); //获取第一个完全显示的item
        T currentGroup = currentGroup(firstVisibleItemPosition); //获取当前所在的组
        if (null == currentGroup) { //当前不存在组
            return;
        }
        View firstCompletelyItemView = linearLayoutManager.findViewByPosition(firstCompletelyVisibleItemPosition); //获取第一个完全显示的Item
        c.save();
        View currentGroupView = getGroupView(currentGroup.getGroupLayoutId(), parent); //当前的组
        measureView(currentGroupView, parent);
        int y = 0;
        int currentGroupViewBottom = y + currentGroupView.getHeight(); //当前组下边位置
        T nextGroup = mGroups.get(firstCompletelyVisibleItemPosition); //离顶部最近的一个组
        if (null != nextGroup) {
            View nextGroupView = getGroupView(nextGroup.getGroupLayoutId(), parent);
            measureView(nextGroupView, parent);
            int nextGroupViewTop = firstCompletelyItemView.getTop() - nextGroupView.getHeight();  //离顶部最近的一个组的上边位置
            if (0 < nextGroupViewTop && nextGroupViewTop < currentGroupViewBottom) {
                y = nextGroupViewTop - currentGroupView.getHeight();
            }
        }
        mHelper.callback(currentGroupView, currentGroup);
        c.translate(0, y);
        currentGroupView.draw(c);
        c.restore();

    }

    /**
     * 获取当前组
     *
     * @param position
     * @return
     */
    private T currentGroup(int position) {
        int size = mGroups.size();
        int groupKey = -1;
        for (int i = 0; i < size; i++) {
            int key = mGroups.keyAt(i);
            if (position < key) {
                continue;
            }
            if (position - groupKey > position - key) { //获取小于position最近的一个组
                groupKey = key;
            }
        }
        if (0 > groupKey) {
            return null;
        }
        return mGroups.get(groupKey);
    }


    private void initPaint() {
        mDividerPaint = new Paint();
        mDividerPaint.setAntiAlias(true);
        mDividerPaint.setColor(defaultDividerColor);
    }

    public GroupDecoration addGroup(int position, T groupBean) {
        mGroups.put(position, groupBean);
        return this;
    }

    public GroupDecoration setDividerHeight(int dividerHeight) {
        this.dividerHeight = dividerHeight;
        return this;
    }

    public GroupDecoration setDividerColor(@ColorInt int colorInt) {
        mDividerPaint.setColor(colorInt);
        return this;
    }

    /**
     * 设置是否吸顶
     *
     * @param pasteTop
     */
    public void setPasteTop(boolean pasteTop) {
        isPasteTop = pasteTop;
    }

    /**
     * 清空分组
     */
    public GroupDecoration clearGroup() {
        mGroups.clear();
        mGroupsView.clear();
        return this;
    }

    private int dp2px(int dp) {
        return AutoSizeUtils.dp2px(mContext, dp);
    }

    private View getGroupView(@LayoutRes int groupLayoutId, ViewGroup parent) {
        View result = null;
        if (0 > mGroupsView.indexOfKey(groupLayoutId)) {
            result = LayoutInflater.from(mContext).inflate(groupLayoutId, parent, false);
            mGroupsView.put(groupLayoutId, result);
        } else {
            result = mGroupsView.get(groupLayoutId);
        }
        return result;
    }

    private View measureView(View view, ViewGroup parent) {
        if (null == view.getLayoutParams()) {
            view.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT));
        }
        int widthSpec = View.MeasureSpec.makeMeasureSpec(parent.getWidth(), View.MeasureSpec.EXACTLY);
        int childWidthSpec = ViewGroup.getChildMeasureSpec(widthSpec, parent.getPaddingLeft() + parent.getPaddingRight(), view.getLayoutParams().width);
        int childHeightSpec;
        if (view.getLayoutParams().height > 0) {
            childHeightSpec = View.MeasureSpec.makeMeasureSpec(view.getLayoutParams().height, View.MeasureSpec.EXACTLY);
        } else {
            childHeightSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        }
        view.measure(childWidthSpec, childHeightSpec);
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        return view;
    }


    public interface Helper<T extends BaseGroupBean> {
        void callback(View groupView, T t);
    }

}
