package com.toocms.wago.dec.timer_shaft;

import android.content.Context;
import android.content.res.Resources;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.view.View;

import androidx.annotation.ColorInt;
import androidx.annotation.DrawableRes;
import androidx.annotation.IdRes;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import me.jessyan.autosize.utils.AutoSizeUtils;

public class TimerShaftDecoration extends RecyclerView.ItemDecoration {
    public static final int WHITE_WRAP_CONTENT = -1; //宽度自适应
    private Gravity mGravity = Gravity.CENTER; //所化的时间轴重力方向

    private Context mContext;

    private int mTimerShaftWidth = WHITE_WRAP_CONTENT; //时间轴整体所占宽度(单位dp)
    @DrawableRes
    private int mDefaultTimeDotResId; //默认时间点样式
    @DrawableRes
    private int mCurrentTimeDotResId; //当前时间点样式

    public final int mDefaultTimeDotWidth = 8; //默认的时间点宽度

    private int mTimerShaftLineColor = 0xFF999999; //时间轴线颜色
    private int mTimerShaftLineWidth = 2; //时间轴线宽度(单位dp)

    private int mLineAndDotInterval = 0; //线和时间点的间隔距离(单位dp)

    private int mOffset; //与重力方向的偏移(Gravity.CENTER:为左边的偏移量)
    @IdRes
    private int mAligningViewId;

    private final int noDefaultDotResDotColor = 0xFF999999;
    private final int noCurrentDotResDotColor = 0xFFFF0000;
    public final int defaultBackgroundColor = 0x00000000; //默认背景色

    private boolean isLineExceedDot = true;

    private Paint mDotPaint, mLinePaint, mBackgroundPaint;

    private int mCurrentDotPosition = -1; //当前时间点

    private int startDrawPosition = 0; //开始绘制的位置


    public TimerShaftDecoration(Context context) {
        this.mContext = context;
        initializePaint();
    }

    private void initializePaint() {
        mDotPaint = new Paint();
        mLinePaint = new Paint();
        mBackgroundPaint = new Paint();
        mBackgroundPaint.setColor(defaultBackgroundColor);
    }


    @Override
    public void onDraw(@NonNull Canvas c, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.onDraw(c, parent, state);
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (!(layoutManager instanceof LinearLayoutManager) || layoutManager instanceof GridLayoutManager) {
            throw new IllegalArgumentException("不支持该布局管理器:" + layoutManager.getClass().getName());
        }
        LinearLayoutManager linearLayoutManager = (LinearLayoutManager) layoutManager;
        int itemCount = parent.getAdapter().getItemCount();
        int count = parent.getChildCount();
        for (int i = 0; i < count; i++) {
            View itemView = parent.getChildAt(i);
            int layoutPosition = parent.getChildLayoutPosition(itemView);
            if (startDrawPosition > layoutPosition) {
                continue;
            }
            if (LinearLayoutManager.VERTICAL == linearLayoutManager.getOrientation()) {
                verticalDraw(c, linearLayoutManager, itemView, itemCount, layoutPosition);
            } else {
                horizontalDraw(c, linearLayoutManager, itemView, itemCount, layoutPosition);
            }
        }
    }

    private void horizontalDraw(Canvas c, LinearLayoutManager layoutManager, View itemView, int itemCount, int position) {
        int decoratedLeft = layoutManager.getDecoratedLeft(itemView);
        int decoratedRight = layoutManager.getDecoratedRight(itemView);
        int itemViewLeft = itemView.getLeft();
        int itemViewRight = itemView.getRight();
        mLinePaint.setColor(mTimerShaftLineColor);
        Drawable dot = null;
        if (mCurrentDotPosition == position) {
            dot = getTimeDot(mCurrentTimeDotResId);
        } else {
            dot = getTimeDot(mDefaultTimeDotResId);
        }
        int aligningCenterX;
        if (0 == mAligningViewId || null == itemView.findViewById(mAligningViewId)) {
            aligningCenterX = (itemViewLeft + itemViewRight) / 2;
        } else {
            View aligningView = itemView.findViewById(mAligningViewId);
            aligningCenterX = itemViewLeft + (aligningView.getLeft() + aligningView.getRight()) / 2;
        }
        //绘制背景
        int backgroundLeft = layoutManager.getDecoratedLeft(itemView);
        int backgroundTop = layoutManager.getDecoratedTop(itemView);
        int backgroundRight = layoutManager.getDecoratedLeft(itemView);
        int backgroundBottom = itemView.getTop();
        if (startDrawPosition == position && !isLineExceedDot) {
            backgroundLeft = itemView.getLeft();
        }
        if (1 == itemCount - position && !isLineExceedDot) {
            backgroundRight = itemView.getRight();
        }
        c.drawRect(backgroundLeft, backgroundTop, backgroundRight, backgroundBottom, mBackgroundPaint);
        //绘制点
        int dotCentreY; //点中心点的Y坐标
        int dotLeft; //点的左边
        int dotRight; //点的右边
        if (null == dot) {
            mDotPaint.setColor(mCurrentDotPosition == position ? noCurrentDotResDotColor : noDefaultDotResDotColor);
            if (Gravity.LEFT == mGravity) {
                dotCentreY = dp2px(mOffset) + dp2px(mDefaultTimeDotWidth) / 2;
            } else if (Gravity.RIGHT == mGravity) {
                dotCentreY = getTimerShaftWidth() - dp2px(mOffset) - dp2px(mDefaultTimeDotWidth) / 2;
            } else {
                dotCentreY = getTimerShaftWidth() / 2 + dp2px(mOffset) - dp2px(mDefaultTimeDotWidth) / 2;
            }
            dotLeft = aligningCenterX - dp2px(mDefaultTimeDotWidth) / 2;
            dotRight = aligningCenterX + dp2px(mDefaultTimeDotWidth) / 2;
            c.drawCircle(aligningCenterX, dotCentreY, dp2px(mDefaultTimeDotWidth) / 2, mDotPaint);
        } else {
            dot.setBounds(0, 0, dot.getIntrinsicWidth(), dot.getIntrinsicHeight());
            int top;
            int left = aligningCenterX - dot.getIntrinsicWidth() / 2;
            dotLeft = left;
            dotRight = left + dot.getIntrinsicWidth();
            if (Gravity.LEFT == mGravity) {
                top = dp2px(mOffset) + (getMaxTimeDotHeight() - dot.getIntrinsicHeight()) / 2;
            } else if (Gravity.RIGHT == mGravity) {
                top = getTimerShaftHeight() - dp2px(mOffset) - getMaxTimeDotHeight() + (getMaxTimeDotHeight() - dot.getIntrinsicHeight()) / 2;
            } else {
                top = getTimerShaftHeight() / 2 + dp2px(mOffset) - getMaxTimeDotHeight() / 2 + (getMaxTimeDotHeight() - dot.getIntrinsicHeight()) / 2;
            }
            dotCentreY = top + dot.getIntrinsicHeight() / 2;
            c.save();
            c.translate(left, top);
            dot.draw(c);
            c.restore();
        }
        mLinePaint.setColor(mTimerShaftLineColor);
        int lineTop = dotCentreY - dp2px(mTimerShaftLineWidth) / 2;
        int lineBottom = dotCentreY + dp2px(mTimerShaftLineWidth) / 2;
        int lineLeft;
        int lineRight;
        //绘制点左边的线
        if (startDrawPosition != position || isLineExceedDot) {
            lineLeft = decoratedLeft;
            lineRight = dotLeft - dp2px(mLineAndDotInterval);
            c.drawRect(lineLeft, lineTop, lineRight, lineBottom, mLinePaint);
        }
        //绘制点右边的线
        if (1 != itemCount - position || isLineExceedDot) {
            lineLeft = dotRight + dp2px(mLineAndDotInterval);
            lineRight = decoratedRight;
            c.drawRect(lineLeft, lineTop, lineRight, lineBottom, mLinePaint);
        }
    }

    private void verticalDraw(Canvas c, LinearLayoutManager layoutManager, View itemView, int itemCount, int position) {
        int decoratedTop = layoutManager.getDecoratedTop(itemView);
        int decoratedBottom = layoutManager.getDecoratedBottom(itemView);
        int itemViewTop = itemView.getTop();
        int itemViewBottom = itemView.getBottom();


        mLinePaint.setColor(mTimerShaftLineColor);
        Drawable dot = null;
        if (mCurrentDotPosition == position) {
            dot = getTimeDot(mCurrentTimeDotResId);
        } else {
            dot = getTimeDot(mDefaultTimeDotResId);
        }
        int aligningCenterY;
        if (0 == mAligningViewId || null == itemView.findViewById(mAligningViewId)) {
            aligningCenterY = (itemViewTop + itemViewBottom) / 2;
        } else {
            View aligningView = itemView.findViewById(mAligningViewId);
            aligningCenterY = itemViewTop + (aligningView.getTop() + aligningView.getBottom()) / 2;
        }
        //绘制背景
        int backgroundLeft = layoutManager.getDecoratedLeft(itemView);
        int backgroundTop = layoutManager.getDecoratedTop(itemView);
        int backgroundRight = itemView.getLeft();
        int backgroundBottom = layoutManager.getDecoratedBottom(itemView);
        if (startDrawPosition == position && !isLineExceedDot) {
            backgroundTop = itemView.getTop();
        }
        if (1 == itemCount - position && !isLineExceedDot) {
            backgroundBottom = itemView.getBottom();
        }
        c.drawRect(backgroundLeft, backgroundTop, backgroundRight, backgroundBottom, mBackgroundPaint);
        //绘制点
        int dotCentreX; //点中心点的X坐标
        int dotTop; //点的顶部
        int dotBottom; //点的底部
        if (null == dot) {
            mDotPaint.setColor(mCurrentDotPosition == position ? noCurrentDotResDotColor : noDefaultDotResDotColor);
            if (Gravity.LEFT == mGravity) {
                dotCentreX = dp2px(mOffset) + dp2px(mDefaultTimeDotWidth) / 2;
            } else if (Gravity.RIGHT == mGravity) {
                dotCentreX = getTimerShaftWidth() - dp2px(mOffset) - dp2px(mDefaultTimeDotWidth) / 2;
            } else {
                dotCentreX = getTimerShaftWidth() / 2 + dp2px(mOffset) - dp2px(mDefaultTimeDotWidth) / 2;
            }
            dotTop = aligningCenterY - dp2px(mDefaultTimeDotWidth) / 2;
            dotBottom = aligningCenterY + dp2px(mDefaultTimeDotWidth) / 2;
            c.drawCircle(dotCentreX, aligningCenterY, dp2px(mDefaultTimeDotWidth) / 2, mDotPaint);
        } else {
            dot.setBounds(0, 0, dot.getIntrinsicWidth(), dot.getIntrinsicHeight());
            int left;
            int top = aligningCenterY - dot.getIntrinsicHeight() / 2;
            dotTop = top;
            dotBottom = top + dot.getIntrinsicHeight();
            if (Gravity.LEFT == mGravity) {
                left = dp2px(mOffset) + (getMaxTimeDotWidth() - dot.getIntrinsicWidth()) / 2;
            } else if (Gravity.RIGHT == mGravity) {
                left = getTimerShaftWidth() - dp2px(mOffset) - getMaxTimeDotWidth() + (getMaxTimeDotWidth() - dot.getIntrinsicWidth()) / 2;
            } else {
                left = getTimerShaftWidth() / 2 + dp2px(mOffset) - getMaxTimeDotWidth() / 2 + (getMaxTimeDotWidth() - dot.getIntrinsicWidth()) / 2;
            }
            dotCentreX = left + dot.getIntrinsicWidth() / 2;
            c.save();
            c.translate(left, top);
            dot.draw(c);
            c.restore();
        }
        mLinePaint.setColor(mTimerShaftLineColor);
        int lineLeft = dotCentreX - dp2px(mTimerShaftLineWidth) / 2;
        int lineRight = dotCentreX + dp2px(mTimerShaftLineWidth) / 2;
        int lineTop;
        int lineBottom;
        //绘制点上边的线
        if (startDrawPosition != position || isLineExceedDot) {
            lineTop = decoratedTop;
            lineBottom = dotTop - dp2px(mLineAndDotInterval);
            c.drawRect(lineLeft, lineTop, lineRight, lineBottom, mLinePaint);
        }
        //绘制点下边的线
        if (1 != itemCount - position || isLineExceedDot) {
            lineTop = dotBottom + dp2px(mLineAndDotInterval);
            lineBottom = decoratedBottom;
            c.drawRect(lineLeft, lineTop, lineRight, lineBottom, mLinePaint);
        }
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        super.getItemOffsets(outRect, view, parent, state);
        RecyclerView.LayoutManager layoutManager = parent.getLayoutManager();
        if (!(layoutManager instanceof LinearLayoutManager)) {
            throw new IllegalArgumentException("不支持该布局管理器:" + layoutManager.getClass().getName());
        }
        LinearLayoutManager linearLayoutManager = (LinearLayoutManager) layoutManager;
        int layoutPosition = parent.getChildLayoutPosition(view);
        if (startDrawPosition > layoutPosition) {
            return;
        }
        if (LinearLayoutManager.VERTICAL == linearLayoutManager.getOrientation()) {
            verticalItemOffsets(outRect, view, parent, state);
        } else {
            horizontalItemOffsets(outRect, view, parent, state);
        }


    }

    private void horizontalItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.top = getTimerShaftHeight();
    }

    private void verticalItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
        outRect.left = getTimerShaftWidth();
    }

    /**
     * 设置时间轴的宽度
     *
     * @param timerShaftWidth
     */

    public void setTimerShaftWidth(int timerShaftWidth) {
        if (0 >= timerShaftWidth) {
            timerShaftWidth = WHITE_WRAP_CONTENT;
        }
        this.mTimerShaftWidth = timerShaftWidth;
    }

    /**
     * 设置当前时间点资源
     *
     * @param currentTimeDotRes 资源文件Id(必须有固定宽高)
     */
    public void setCurrentTimeDotRes(@DrawableRes int currentTimeDotRes) {
        checkDrawable(currentTimeDotRes);
        this.mCurrentTimeDotResId = currentTimeDotRes;
    }

    /**
     * 设置默认时间点资源
     *
     * @param defaultTimeDotRes 资源文件Id(必须有固定宽高)
     */
    public void setDefaultTimeDotRes(@DrawableRes int defaultTimeDotRes) {
        checkDrawable(defaultTimeDotRes);
        this.mDefaultTimeDotResId = defaultTimeDotRes;
    }

    /**
     * 设置重力方向
     *
     * @param gravity
     */
    public void setGravity(Gravity gravity) {
        this.mGravity = gravity;
    }

    /**
     * 设置时间轴线的颜色
     *
     * @param timerShaftLineColor
     */

    public void setTimerShaftLineColor(@ColorInt int timerShaftLineColor) {
        this.mTimerShaftLineColor = timerShaftLineColor;
    }

    /**
     * 设置偏移
     *
     * @param offset
     */
    public void setOffset(int offset) {
        this.mOffset = offset;
    }

    public void setStartDrawPosition(int startDrawPosition) {
        this.startDrawPosition = startDrawPosition;
    }

    /**
     * 设置背景颜色
     * @param backgroundColor
     */
    public void setBackgroundColor(@ColorInt int backgroundColor) {
        mBackgroundPaint.setColor(backgroundColor);
    }


    /**
     * 设置时间轴线的宽度
     *
     * @param timerShaftLineWidth
     */
    public void setTimerShaftLineWidth(int timerShaftLineWidth) {
        this.mTimerShaftLineWidth = timerShaftLineWidth;
    }

    /**
     * 设置线是否超过首尾两个点
     *
     * @param isLineExceedDot
     */

    public void setLineExceedDot(boolean isLineExceedDot) {
        this.isLineExceedDot = isLineExceedDot;
    }

    /**
     * 设置当前时间点的位置
     *
     * @param currentDotPosition
     */
    public void setCurrentDotPosition(int currentDotPosition) {
        this.mCurrentDotPosition = currentDotPosition;
    }

    /**
     * 设置点和线之间的间隔
     *
     * @param lineAndDotInterval
     */
    public void setLineAndDotInterval(int lineAndDotInterval) {
        this.mLineAndDotInterval = lineAndDotInterval;
    }

    /**
     * 时间点对齐的View
     *
     * @param aligningViewId 要对齐的控件Id
     */

    public void setAligningViewId(int aligningViewId) {
        this.mAligningViewId = aligningViewId;
    }

    /**
     * 获取时间轴整体宽度
     *
     * @return
     */
    private int getTimerShaftWidth() {
        int result = 0;
        int maxTimeDotWidth = getMaxTimeDotWidth();
        if (WHITE_WRAP_CONTENT == mTimerShaftWidth) {
            result = maxTimeDotWidth;
        } else {
            result = dp2px(mTimerShaftWidth);
            if (result < maxTimeDotWidth) {
                result = maxTimeDotWidth;
            }
        }
        return result;
    }

    /**
     * 获取时间轴整体宽度
     *
     * @return
     */
    private int getTimerShaftHeight() {
        int result = 0;
        int maxTimeDotHight = getMaxTimeDotHeight();
        if (WHITE_WRAP_CONTENT == mTimerShaftWidth) {
            result = maxTimeDotHight;
        } else {
            result = dp2px(mTimerShaftWidth);
            if (result < maxTimeDotHight) {
                result = maxTimeDotHight;
            }
        }
        return result;
    }


    /**
     * 获取时间点最大的宽度
     *
     * @return
     */
    private int getMaxTimeDotWidth() {
        Drawable defaultTimeDotRes = getTimeDot(mDefaultTimeDotResId);
        Drawable currentTimeDotRes = getTimeDot(mCurrentTimeDotResId);
        int defaultTimeDotWidth = dp2px(mDefaultTimeDotWidth);
        int currentTimeDotWidth = dp2px(mDefaultTimeDotWidth);
        if (null != defaultTimeDotRes) {
            defaultTimeDotWidth = defaultTimeDotRes.getIntrinsicWidth();
        }
        if (null != currentTimeDotRes) {
            currentTimeDotWidth = currentTimeDotRes.getIntrinsicWidth();
        }
        return defaultTimeDotWidth > currentTimeDotWidth ? defaultTimeDotWidth : currentTimeDotWidth;
    }


    /**
     * 获取时间点最大的高度
     *
     * @return
     */
    private int getMaxTimeDotHeight() {
        Drawable defaultTimeDotRes = getTimeDot(mDefaultTimeDotResId);
        Drawable currentTimeDotRes = getTimeDot(mCurrentTimeDotResId);
        int defaultTimeDotWidth = dp2px(mDefaultTimeDotWidth);
        int currentTimeDotWidth = dp2px(mDefaultTimeDotWidth);
        if (null != defaultTimeDotRes) {
            defaultTimeDotWidth = defaultTimeDotRes.getIntrinsicHeight();
        }
        if (null != currentTimeDotRes) {
            currentTimeDotWidth = currentTimeDotRes.getIntrinsicHeight();
        }
        return defaultTimeDotWidth > currentTimeDotWidth ? defaultTimeDotWidth : currentTimeDotWidth;
    }


    private void checkDrawable(@DrawableRes int resId) {
        Drawable timeDot = getTimeDot(resId);
        if (0 >= timeDot.getIntrinsicWidth() || 0 >= timeDot.getIntrinsicHeight()) {
            String resourceName = mContext.getResources().getResourceName(resId);
            throw new IllegalArgumentException("所设置资源(" + resourceName + ")没有设置size(width:" + timeDot.getIntrinsicWidth() + " height:" + timeDot.getIntrinsicHeight() + ")");
        }
    }

    private Drawable getTimeDot(@DrawableRes int dotResId) {
        Resources resources = mContext.getResources();
        Drawable result = null;
        if (0 != dotResId) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                result = resources.getDrawable(dotResId, mContext.getTheme());
            } else {
                result = resources.getDrawable(dotResId);
            }
        }
        return result;
    }


    private int dp2px(int dp) {
        return AutoSizeUtils.dp2px(mContext, dp);
    }


    public enum Gravity {
        LEFT, CENTER, RIGHT;
    }
}
