package com.toocms.wago.dialog.option;

import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.BarUtils;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.R;
import com.toocms.wago.dec.DpLinearLayoutDecoration;
import com.toocms.wago.dialog.option.adt.OptionAdt;

import java.util.ArrayList;
import java.util.List;

public class OptionDialog<T> extends DialogFragment {

    private RecyclerView contentRv;
    private Callback<T> callback;
    private OptionAdt<T> optionAdt;
    private List<T> optionItems;
    private List<Integer> selectedPositions = new ArrayList<>();
    private int maxSelectedCount = -1;
    private int itemViewId;
    private int targetViewBottom;

    public OptionDialog(int itemViewId, Callback<T> callback) {
        this.itemViewId = itemViewId;
        this.callback = callback;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View contentView = inflater.inflate(R.layout.dialog_option, container, false);
        contentRv = contentView.findViewById(R.id.content_rv);
        contentRv.setLayoutManager(new LinearLayoutManager(getContext()));
        ((DefaultItemAnimator) contentRv.getItemAnimator()).setSupportsChangeAnimations(false);
        contentRv.addItemDecoration(new DpLinearLayoutDecoration(getContext(), 1, 1));
        optionAdt = new OptionAdt<T>() {
            @Override
            public void onBindItem(Holder holder, T data, boolean isSelected) {
                callback.onBindItem(holder, data, isSelected);
            }
        };
        optionAdt.setItemViewId(itemViewId);
        optionAdt.setMaxSelectedCount(maxSelectedCount);
        optionAdt.setSelectedPositions(selectedPositions);
        optionAdt.setOnItemClickListener((holder, itemView, position) -> {
            optionAdt.addSelectedPosition(position);
            dismiss();
        });
        optionAdt.setOptionItems(optionItems);
        contentRv.setAdapter(optionAdt);
        return contentView;
    }

    @Override
    public void onStart() {
        super.onStart();
        Dialog dialog = getDialog();
        if (null == dialog) return;
        dialog.setCancelable(true);
        dialog.setCanceledOnTouchOutside(true);
        Window window = dialog.getWindow();
        if (null == window) return;
        window.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        window.setBackgroundDrawable(new ColorDrawable(Color.TRANSPARENT));
        window.setDimAmount(0);
        window.setGravity(Gravity.TOP);
        WindowManager.LayoutParams attributes = window.getAttributes();
        if (null == attributes)
            attributes = new WindowManager.LayoutParams(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.WRAP_CONTENT);
        attributes.x = 0;
        attributes.y = targetViewBottom - statusBarHeight();
        window.setAttributes(attributes);
    }

    @Override
    public void onResume() {
        super.onResume();
//        BarUtils.setNavBarLightMode(getActivity().getWindow(), true);
    }

    private int statusBarHeight() {
//        return BarUtils.getActionBarHeight();
        int identifier = getResources().getIdentifier("status_bar_height", "dimen", "android");
        return getResources().getDimensionPixelSize(identifier);
    }

    @Override
    public void onDismiss(@NonNull DialogInterface dialog) {
        super.onDismiss(dialog);
        List<T> selectedItems = new ArrayList<>();
        if (null != optionAdt) {
            List<Integer> selectedPositions = optionAdt.getSelectedPositions();
            int size = selectedPositions.size();
            for (int i = 0; i < size; i++) {
                T optionItem = optionAdt.getOptionItem(selectedPositions.get(i));
                if (null == optionItem) {
                    continue;
                }
                selectedItems.add(optionItem);
            }
        }
        if (null != callback) {
            callback.onSelectedItems(selectedItems);
        }
    }


    public OptionDialog<T> setCallback(Callback<T> callback) {
        this.callback = callback;
        return this;
    }

    public OptionDialog<T> setOptionItems(List<T> optionItems) {
        this.optionItems = optionItems;
        return this;
    }

    public OptionDialog<T> setMaxSelectedCount(int count) {
        this.maxSelectedCount = count;
        return this;
    }

    public OptionDialog<T> targetView(View target) {
        int[] location = new int[2];
        target.getLocationInWindow(location);
        targetViewBottom = location[1] + target.getHeight();
        return this;
    }

    /**
     * 选择的选项
     *
     * @param selectedItems 选择的item的position集合
     */
    public OptionDialog<T> setSelectedItems(int... selectedItems) {
        for (int position : selectedItems) {
            selectedPositions.add(position);
        }
        return this;
    }

    public interface Callback<T> {

        void onBindItem(OptionAdt.Holder holder, T data, boolean isSelected);

        void onSelectedItems(List<T> items);
    }

}
