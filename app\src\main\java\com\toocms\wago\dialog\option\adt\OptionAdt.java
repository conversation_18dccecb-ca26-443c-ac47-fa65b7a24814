package com.toocms.wago.dialog.option.adt;

import android.util.SparseArray;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.blankj.utilcode.util.LogUtils;
import com.toocms.wago.R;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

public abstract class OptionAdt<T> extends RecyclerView.Adapter<OptionAdt.Holder> {
    private List<T> optionItems;
    private List<Integer> selectedPositions = new ArrayList<>();
    private OnItemClickListener mOnItemClickListener;
    private int maxSelectedCount = -1;
    private int itemViewId;

    @NonNull
    @Override
    public Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(itemViewId, parent, false);
        return new Holder(itemView).setOnItemClickListener(mOnItemClickListener);
    }

    @Override
    public void onBindViewHolder(@NonNull Holder holder, int position) {
        holder.itemView.setTag(R.id.tag_item, position);
        onBindItem(holder, optionItems.get(position), isItemSelected(position));
    }

    @Override
    public int getItemCount() {
        return null != optionItems ? optionItems.size() : 0;
    }

    public void setOptionItems(List<T> optionItems) {
        this.optionItems = optionItems;
        notifyDataSetChanged();
    }

    public T getOptionItem(int position) {
        if (0 > position || getItemCount() <= position) {
            return null;
        }
        return optionItems.get(position);
    }

    public List<Integer> getSelectedPositions() {
        return selectedPositions;
    }

    private boolean isItemSelected(int position) {
        int index = selectedPositions.indexOf(position);
        return 0 <= index;
    }

    public void setItemViewId(int itemViewId) {
        this.itemViewId = itemViewId;
    }

    public void setSelectedPositions(List<Integer> selectedPositions) {
        if (null != selectedPositions) {
            this.selectedPositions.addAll(selectedPositions);
        }
        notifyDataSetChanged();
    }

    public void addSelectedPosition(int position) {
        int index = selectedPositions.indexOf(position);
        if (-1 < index) {
            selectedPositions.remove(index);
        } else {
            if (1 > maxSelectedCount) {
                selectedPositions.add(position);
            } else {
                while (maxSelectedCount <= selectedPositions.size()) {
                    selectedPositions.remove(0);
                }
                selectedPositions.add(position);
            }
        }
        notifyDataSetChanged();
    }

    public abstract void onBindItem(Holder holder, T data, boolean isSelected);

    public void setMaxSelectedCount(int maxSelectedCount) {
        this.maxSelectedCount = maxSelectedCount;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.mOnItemClickListener = listener;
    }


    public static class Holder extends RecyclerView.ViewHolder {
        private SparseArray<View> views = new SparseArray<>();
        private OnItemClickListener mOnItemClickListener;

        public Holder(@NonNull View itemView) {
            super(itemView);
            itemView.setOnClickListener(v -> {
                if (null != mOnItemClickListener) {
                    mOnItemClickListener.onItem(Holder.this, Holder.this.itemView, (int) v.getTag(R.id.tag_item));
                }
            });
        }

        public <T extends View> View getView(@NonNull int viewId) {
            int index = views.indexOfKey(viewId);
            T result;
            if (0 > index) {
                result = itemView.findViewById(viewId);
                views.put(viewId, result);
            } else {
                result = (T) views.get(viewId);
            }
            return result;
        }

        public Holder setText(int viewId, CharSequence content) {
            View view = getView(viewId);
            if (view instanceof TextView) {
                ((TextView) view).setText(content);
            }
            return this;
        }

        public void viewSelected(boolean isSelected) {
            itemView.setSelected(isSelected);
        }

        public Holder setOnItemClickListener(OnItemClickListener listener) {
            this.mOnItemClickListener = listener;
            return this;
        }

    }


    public interface OnItemClickListener {
        void onItem(Holder holder, View itemView, int position);
    }
}
