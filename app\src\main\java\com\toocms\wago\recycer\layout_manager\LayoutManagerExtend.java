package com.toocms.wago.recycer.layout_manager;

import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers;

public class LayoutManagerExtend extends LayoutManagers {

    public static LayoutManagerFactory linerVerticalCannotScroll() {
        return recyclerView -> new LinearLayoutManager(recyclerView.getContext()) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
    }

    public static LayoutManagerFactory gridVerticalCannotScroll(int spanCount) {
        return recyclerView -> new GridLayoutManager(recyclerView.getContext(), spanCount) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        };
    }
}
