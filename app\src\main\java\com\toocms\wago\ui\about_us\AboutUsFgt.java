package com.toocms.wago.ui.about_us;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtAboutUsBinding;

public class AboutUsFgt extends BaseFragment<FgtAboutUsBinding, AboutUsModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.removeAllLeftViews();
        topBar.setTitle("关于我们");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_about_us;
    }

    @Override
    public int getVariableId() {
        return BR.aboutUsModel;
    }

    @Override
    protected void viewObserver() {

    }
}
