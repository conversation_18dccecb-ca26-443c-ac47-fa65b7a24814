package com.toocms.wago.ui.about_us;

import android.app.Application;
import android.content.Intent;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.IntentUtils;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.ui.about_us.detail.WagoDetailFgt;
import com.toocms.wago.ui.about_us.services.list.ServicesListFgt;
import com.toocms.wago.ui.search.SearchFgt;

public class AboutUsModel extends BaseViewModel<BaseModel> {

    public AboutUsModel(@NonNull Application application) {
        super(application);
    }

    public BindingCommand startWagoDetail = new BindingCommand(() -> {
        startFragment(WagoDetailFgt.class);
    });

    public BindingCommand startServicesList = new BindingCommand(() -> {
        startFragment(ServicesListFgt.class);
    });

    public BindingCommand call = new BindingCommand(() -> {
        TooCMSApplication.getInstance().startActivity(IntentUtils.getDialIntent("4008692333"));
    });
}
