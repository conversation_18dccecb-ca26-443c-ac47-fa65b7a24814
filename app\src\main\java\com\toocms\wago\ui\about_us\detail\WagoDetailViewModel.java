package com.toocms.wago.ui.about_us.detail;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.bean.WagoDetailBean;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/22
 */
public class WagoDetailViewModel extends BaseViewModel {

    public ObservableField<String> html = new ObservableField<>();

    public WagoDetailViewModel(@NonNull @NotNull Application application) {
        super(application);
        selectByDetail();
    }

    private void selectByDetail() {
        ApiTool.post("content/selectByDetail")
                .asTooCMSResponse(WagoDetailBean.class)
                .withViewModel(this)
                .request(wagoDetailBean -> {
                    html.set(wagoDetailBean.content);
                });
    }
}
