package com.toocms.wago.ui.about_us.services.detail;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtServicesDetailBinding;

/**
 * Author：Zero
 * Date：2021/6/23
 */
public class ServicesDetailFgt extends BaseFragment<FgtServicesDetailBinding, ServicesDetailViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle(getArguments().getString("title"));
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_services_detail;
    }

    @Override
    public int getVariableId() {
        return BR.servicesDetailViewModel;
    }

    @Override
    protected ServicesDetailViewModel getViewModel() {
        return new ServicesDetailViewModel(TooCMSApplication.getInstance(), getArguments().getString("id"));
    }

    @Override
    protected void viewObserver() {

    }
}
