package com.toocms.wago.ui.about_us.services.detail;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.IntentUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.bean.ServicesDetailBean;

/**
 * Author：Zero
 * Date：2021/6/23
 */
public class ServicesDetailViewModel extends BaseViewModel {

    public String id;
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> fun = new ObservableField<>();
    public ObservableField<String> telephone = new ObservableField<>();
    public ObservableField<String> email = new ObservableField<>();

    public ServicesDetailViewModel(@NonNull Application application, String id) {
        super(application);
        this.id = id;
        selectByCustomerDetails();
    }

    public BindingCommand call = new BindingCommand(() -> {
        ActivityUtils.getTopActivity().startActivity(IntentUtils.getDialIntent(telephone.get()));
    });

    public void selectByCustomerDetails() {
        ApiTool.get("customer/selectByCustomerDetails")
                .add("customerServiceId", id)
                .asTooCMSResponse(ServicesDetailBean.class)
                .withViewModel(this)
                .request(servicesDetailBean -> {
                    title.set(servicesDetailBean.title);
                    fun.set(servicesDetailBean.fun);
                    telephone.set(servicesDetailBean.telephone);
                    email.set(servicesDetailBean.email);
                });
    }
}
