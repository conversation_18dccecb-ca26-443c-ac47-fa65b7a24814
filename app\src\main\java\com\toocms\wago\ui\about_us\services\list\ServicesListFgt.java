package com.toocms.wago.ui.about_us.services.list;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtSerivesListBinding;

/**
 * Author：Zero
 * Date：2021/6/23
 */
public class ServicesListFgt extends BaseFragment<FgtSerivesListBinding, ServicesListViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("客户服务");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_serives_list;
    }

    @Override
    public int getVariableId() {
        return BR.servicesListViewModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
