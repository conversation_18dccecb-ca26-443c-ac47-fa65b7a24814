package com.toocms.wago.ui.about_us.services.list;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ServicesListBean;
import com.toocms.wago.ui.about_us.services.detail.ServicesDetailFgt;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/23
 */
public class ServicesListItemViewModel extends ItemViewModel<ServicesListViewModel> {

    public ObservableField<String> title = new ObservableField<>();
    public String id;

    public ServicesListItemViewModel(@NonNull @NotNull ServicesListViewModel viewModel, ServicesListBean.RowsBean rowsBean) {
        super(viewModel);
        id = rowsBean.customerServiceId;
        title.set(rowsBean.title);
    }

    public BindingCommand startDetail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("title", title.get());
        bundle.putString("id", id);
        viewModel.startFragment(ServicesDetailFgt.class, bundle);
    });
}
