package com.toocms.wago.ui.about_us.services.list;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.network.exception.OnError;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.ServicesListBean;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/23
 */
public class ServicesListViewModel extends BaseViewModel {

    public int p = 1;

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<ServicesListItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<ServicesListItemViewModel> itemBinding = ItemBinding.of(BR.servicesListItemViewModel, R.layout.listitem_services);

    public ServicesListViewModel(@NonNull @NotNull Application application) {
        super(application);
        selectByCustomerList(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByCustomerList(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByCustomerList(false);
    });

    public void selectByCustomerList(boolean isShowLoading) {
        ApiTool.postJson("customer/selectByCustomerList")
                .add("currentPage", p)
                .add("pageSize", 0)
                .add("query", "")
                .asTooCMSResponse(ServicesListBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(servicesListBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(servicesListBean.rows, (index, item) -> {
                        items.add(new ServicesListItemViewModel(this, item));
                    });
                });
    }
}
