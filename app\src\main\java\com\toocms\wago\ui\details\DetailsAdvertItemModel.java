package com.toocms.wago.ui.details;

import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.core.content.FileProvider;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.JsonUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.configs.FileManager;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.network.RxHttp;
import com.toocms.tab.share.TabShare;
import com.toocms.tab.share.listener.OnShareListener;
import com.toocms.tab.widget.banner.BannerItem;
import com.toocms.tab.widget.banner.base.BaseBanner;
import com.toocms.wago.bean.ProductDetailBean;
import com.toocms.wago.bean.ShareInfoBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.login.LoginFgt;
import com.toocms.wago.ui.photoview.PhotoViewFgt;
import com.umeng.socialize.bean.SHARE_MEDIA;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;

public class DetailsAdvertItemModel extends MultiItemViewModel<DetailsModel> {

    public ObservableList<ProductDetailBean.ProductBean.BannerListBean> bannerItems = new ObservableArrayList<>();
    public ArrayList<String> images = new ArrayList<>();
    public String productId;
    public String pdfUrl;
    public String type;
    public String share_url, share_title, share_desc, share_picture;

    public DetailsAdvertItemModel(@NonNull DetailsModel viewModel, List<ProductDetailBean.ProductBean.BannerListBean> bannerItems, String productId, String detailType) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_ONE);
        this.bannerItems.addAll(bannerItems);
        CollectionUtils.forAllDo(bannerItems, (index, item) -> {
            if (item.isImg) images.add(item.bannerUrl);
        });
        this.productId = productId;
        type = detailType;
        share(detailType);
    }

    public DetailsAdvertItemModel(@NonNull DetailsModel viewModel, List<ProductDetailBean.ProductBean.BannerListBean> bannerItems, String productId, String pdfUrl, String detailType) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_ONE);
        this.bannerItems.addAll(bannerItems);
        CollectionUtils.forAllDo(bannerItems, (index, item) -> {
            if (item.isImg) images.add(item.bannerUrl);
        });
        this.pdfUrl = pdfUrl;
        this.productId = productId;
        type = detailType;
        share(detailType);
    }

    public BaseBanner.OnItemClickListener<ProductDetailBean.ProductBean.BannerListBean> onItemClickListener = (view, item, position) -> {
        Bundle bundle = new Bundle();
        bundle.putStringArrayList(Constants.KEY_IMAGES, images);
        bundle.putInt(Constants.KEY_POSITION, position);
        viewModel.startFragment(PhotoViewFgt.class, bundle);
    };

    public BindingCommand collect = new BindingCommand(() -> {
        if (!UserRepository.getInstance().isLogin()) {
            viewModel.startFragment(LoginFgt.class);
            return;
        }
        addCollect();
    });

    public BindingCommand download = new BindingCommand(() -> {
        if (!UserRepository.getInstance().isLogin()) {
            viewModel.startFragment(LoginFgt.class);
            return;
        }
        updownload();
    });

    public BindingCommand share = new BindingCommand(() -> {
        if (!UserRepository.getInstance().isLogin()) {
            viewModel.startFragment(LoginFgt.class);
            return;
        }
        TabShare.getOneKeyShare()
                .setPlatform(SHARE_MEDIA.WEIXIN, SHARE_MEDIA.WEIXIN_CIRCLE)
                .setUrl(share_url, share_title, share_desc, share_picture)
                .setShareCallback(new OnShareListener() {
                    @Override
                    public void onResult(SHARE_MEDIA share_media) {
                        viewModel.showToast("分享成功");
                    }
                }).share();
    });

    private void share(String detailType) {
        ApiTool.get("share/share")
                .add("productId", productId)
                .add("userid", UserRepository.getInstance().getUser().id)
                .add("type", detailType)
                .asTooCMSResponse(ShareInfoBean.class)
                .request(shareInfoBean -> {
                    share_url = shareInfoBean.url;
                    share_title = shareInfoBean.title;
                    share_desc = shareInfoBean.subtitle;
                    share_picture = shareInfoBean.imgurl;
                });
    }

    private void addCollect() {
        ApiTool.get("collect/addCollect")
//                .addHeader("Authorization", UserRepository.getInstance().getUser().token)
                .add("productId", productId)
                .add("userid", UserRepository.getInstance().getUser().id)
                .add("type", type)
                .asTooCMSResponse(String.class)
                .withViewModel(viewModel)
                .request(s -> {
                    viewModel.showToast(s);
                });
    }

    private void updownload() {
        if (StringUtils.isEmpty(pdfUrl))
            RxHttp.get(TooCMSApplication.getInstance().getAppConfig().getBaseUrl() + "product/updownload")
                    .add("productId", productId)
                    .add("userId", UserRepository.getInstance().getUser().id)
                    .add("type", type)
                    .asString()
                    .subscribe(json -> {
                        String pdfUrl = JsonUtils.getString(json, "data");
                        download(pdfUrl);
                    });
        else download(pdfUrl);
    }

    private void download(String pdfUrl) {
        if (StringUtils.isEmpty(pdfUrl)) {
            viewModel.showToast("无效的URL");
            return;
        }
        String filePath = FileManager.getDownloadPath() + pdfUrl.substring(pdfUrl.lastIndexOf("/"));
        if (FileUtils.isFileExists(filePath)) {
            openPDF(filePath);
        } else
            ApiTool.get(pdfUrl)
                    .asDownload(filePath, progress -> viewModel.showProgress())
                    .onFinally(() -> viewModel.removeProgress())
                    .observeOn(AndroidSchedulers.mainThread())
                    .request(this::openPDF);
    }

    private void openPDF(String path) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.addCategory(Intent.CATEGORY_DEFAULT);
        intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION | Intent.FLAG_ACTIVITY_NEW_TASK);
        Uri parse;
        if (Build.VERSION_CODES.N <= Build.VERSION.SDK_INT) {
            parse = FileProvider.getUriForFile(viewModel.getApplication(), viewModel.getApplication().getPackageName() + ".updateFileProvider", new File(path));
        } else {
            parse = Uri.parse(path);
        }
        intent.setDataAndType(parse, "application/pdf");
        viewModel.getApplication().startActivity(intent);
    }
}
