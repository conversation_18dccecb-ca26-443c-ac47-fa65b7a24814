package com.toocms.wago.ui.details;

import android.annotation.SuppressLint;
import android.webkit.WebSettings;
import android.webkit.WebView;

import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtDetailsBinding;
import com.toocms.wago.ui.web.WebFgt;

public class DetailsFgt extends BaseFragment<FgtDetailsBinding, DetailsModel> {

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onFragmentCreated() {
//        topBar.setTitle(getArguments().getString("productName"));
        WebSettings settings = binding.webview.getSettings();
        settings.setJavaScriptEnabled(true);
        binding.webview.setWebViewClient(new AdvertWebViewClient());
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_details;
    }

    @Override
    public int getVariableId() {
        return BR.detailsModel;
    }

    @Override
    protected DetailsModel getViewModel() {
        return new DetailsModel(TooCMSApplication.getInstance(), getArguments().getString("productId"), getArguments().getString("detailType"));
    }

    @Override
    protected void viewObserver() {
        viewModel.title.observe(this, s -> topBar.setTitle(s));
    }

    public class AdvertWebViewClient extends QMUIWebViewClient {

        public AdvertWebViewClient() {
            super(true, true);
        }

    }
}