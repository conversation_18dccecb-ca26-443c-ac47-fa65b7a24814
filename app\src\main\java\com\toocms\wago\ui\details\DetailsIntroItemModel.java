package com.toocms.wago.ui.details;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.config.Constants;

public class DetailsIntroItemModel extends MultiItemViewModel<DetailsModel> {

    public ObservableArrayList<DetailsIntroItemItemModel> items=new ObservableArrayList<>();
    public ItemBinding<DetailsIntroItemItemModel> itemBinding=ItemBinding.of(BR.detailsIntroItemItemModel, R.layout.listitem_details_intro_item);


    public DetailsIntroItemModel(@NonNull DetailsModel viewModel) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_FOUR);
        items.add(new DetailsIntroItemItemModel(viewModel));
        items.add(new DetailsIntroItemItemModel(viewModel));
        items.add(new DetailsIntroItemItemModel(viewModel));
    }

}
