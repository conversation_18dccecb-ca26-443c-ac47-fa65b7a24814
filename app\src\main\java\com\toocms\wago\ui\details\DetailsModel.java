package com.toocms.wago.ui.details;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.share.TabShare;
import com.toocms.tab.share.listener.OnShareListener;
import com.toocms.tab.widget.banner.BannerItem;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CertificateFileBean;
import com.toocms.wago.bean.ManualFileBean;
import com.toocms.wago.bean.ProductDetailBean;
import com.toocms.wago.bean.ProductPlanBean;
import com.toocms.wago.bean.ShareInfoBean;
import com.toocms.wago.bean.User;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.login.LoginFgt;
import com.umeng.socialize.bean.SHARE_MEDIA;

import java.util.ArrayList;
import java.util.List;

public class DetailsModel extends BaseViewModel<BaseModel> {

    public String productId;
    public String detailType;
    public String type;
    public ObservableField<String> content = new ObservableField<>();
    public SingleLiveEvent<String> title = new SingleLiveEvent<>();

    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {
        if (item instanceof DetailsAdvertItemModel) {
            binding.set(BR.detailsAdvertItemModel, R.layout.layout_details_advert);
        } else if (item instanceof DetailsPreviewItemModel) {
            binding.set(BR.detailsPreviewItemModel, R.layout.layout_details_preview);
        } else if (item instanceof DetailsParamItemModel) {
            binding.set(BR.detailsParamItemModel, R.layout.layout_details_param);
        } else if (item instanceof DetailsIntroItemModel) {
            binding.set(BR.detailsIntroModel, R.layout.layout_details_intro);
        } else if (item instanceof DetailsParamFloatItemModel) {
            binding.set(BR.detailsParamFloatItemModel, R.layout.layout_details_param_float);
        }
    });

    public DetailsModel(@NonNull Application application, String productId, String detailType) {
        super(application);
        this.productId = productId;
        this.detailType = detailType;
        switch (detailType) {
            case Constants.DETAIL_TYPE_PRODUCT:
                type = "1";
                selectByProductDetail();
//                insertBrowser(type);
                break;
            case Constants.DETAIL_TYPE_PRODUCT_PLANS:
                type = "4";
                selectByDetail();
                if (UserRepository.getInstance().isLogin()) {
                    insertBrowser(type);
                }
                break;
            case Constants.DETAIL_TYPE_MATERIAL_FILE:
                type = "3";
                selectProductManualAPPById();
                if (UserRepository.getInstance().isLogin()) {
                    insertBrowser(type);
                }
                break;
            case Constants.DETAIL_TYPE_CERTIFICATE_FILE:
                type = "2";
                selectCertificateAPPById();
                if (UserRepository.getInstance().isLogin()) {
                    insertBrowser(type);
                }
                break;
        }
    }

    private void insertBrowser(String type) {
        ApiTool.postJson("browse/insertBrowser")
                .add("userid", UserRepository.getInstance().getUser().id)
                .add("productid", productId)
                .add("type", type)
                .asTooCMSResponse(String.class)
                .request();
    }

    private void selectByProductDetail() {
        ApiTool.get("product/selectByProductDetail")
                .add("productId", productId)
                .add("userId", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(ProductDetailBean.class)
                .withViewModel(this)
                .request(productDetailBean -> {
                    title.postValue(productDetailBean.product.totalTitle);
                    List<BannerItem> bannerItems = new ArrayList<>();
                    CollectionUtils.forAllDo(productDetailBean.bannerList, (index, item) -> {
                        BannerItem bannerItem = new BannerItem();
                        bannerItem.imgUrl = item.bannerUrl;
                        bannerItems.add(bannerItem);
                    });
                    items.add(new DetailsAdvertItemModel(this, productDetailBean.product.bannerList, productDetailBean.product.productId, type));
                    CollectionUtils.forAllDo(productDetailBean.list, (index, item) -> {
                        items.add(new DetailsParamItemModel(this, item));
                    });
                });
    }

    private void selectByDetail() {
        ApiTool.get("pPlan/selectByDetail")
                .add("productPlanId", productId)
                .asTooCMSResponse(ProductPlanBean.class)
                .withViewModel(this)
                .request(productPlanBean -> {
                    content.set(productPlanBean.content);
                });
    }

    private void selectProductManualAPPById() {
        ApiTool.get("productManual/manual/selectProductManualAPPById")
                .add("id", productId)
                .asTooCMSResponse(ManualFileBean.class)
                .withViewModel(this)
                .request(manualFileBean -> {
                    List<BannerItem> bannerItems = new ArrayList<>();
                    BannerItem bannerItem = new BannerItem();
                    bannerItem.imgUrl = manualFileBean.imgUrl;
                    bannerItems.add(bannerItem);
                    List<ProductDetailBean.ProductBean.BannerListBean> bannerListBeans = new ArrayList<>();
                    ProductDetailBean.ProductBean.BannerListBean bannerListBean = new ProductDetailBean.ProductBean.BannerListBean();
                    bannerListBean.isImg = true;
                    bannerListBean.bannerUrl = manualFileBean.imgUrl;
                    bannerListBeans.add(bannerListBean);
                    items.add(new DetailsAdvertItemModel(this, bannerListBeans, manualFileBean.productManualId, manualFileBean.productManualUrl, type));
                    items.add(new DetailsPreviewItemModel(this, manualFileBean.jpgUrl));
                });
    }

    private void selectCertificateAPPById() {
        ApiTool.get("certificate/selectCertificateAPPById")
                .add("id", productId)
                .asTooCMSResponse(CertificateFileBean.class)
                .withViewModel(this)
                .request(certificateFileBean -> {
                    List<BannerItem> bannerItems = new ArrayList<>();
                    BannerItem bannerItem = new BannerItem();
                    bannerItem.imgUrl = certificateFileBean.imgUrl;
                    bannerItems.add(bannerItem);
                    List<ProductDetailBean.ProductBean.BannerListBean> bannerListBeans = new ArrayList<>();
                    ProductDetailBean.ProductBean.BannerListBean bannerListBean = new ProductDetailBean.ProductBean.BannerListBean();
                    bannerListBean.isImg = true;
                    bannerListBean.bannerUrl = certificateFileBean.imgUrl;
                    bannerListBeans.add(bannerListBean);
                    items.add(new DetailsAdvertItemModel(this, bannerListBeans, certificateFileBean.certificateId, certificateFileBean.certificateUrl, type));
                    items.add(new DetailsPreviewItemModel(this, certificateFileBean.jpgUrl));
                });
    }
}
