package com.toocms.wago.ui.details;

import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.R;
import com.toocms.wago.config.Constants;

public class DetailsParamFloatItemModel extends MultiItemViewModel<DetailsModel> {
    public ObservableField<Boolean> isSelected = new ObservableField<>();
    public ObservableArrayList<View> params = new ObservableArrayList<>();
    public BindingCommand onTitleClickBindingCommand = new BindingCommand(() -> {
        isSelected.set(!isSelected.get());
    });

    public DetailsParamFloatItemModel(@NonNull DetailsModel viewModel) {
        super(viewModel);
        isSelected.set(false);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_FIVE);
        params.add(createParamItem("电源装置和电缆"));
        params.add(createParamItem("USB线缆"));
        params.add(createParamItem("2x轧混"));
        params.add(createParamItem("1x卷轴"));
        params.add(createParamItem("1x色带"));
        params.add(createParamItem("1×标记条和WMB卷装标记牌"));
    }

    private View createParamItem(String param) {
        View inflate = LayoutInflater.from(TooCMSApplication.getInstance()).inflate(R.layout.listitem_details_param_float_item, null);
        TextView contentTv = inflate.findViewById(R.id.content_tv);
        contentTv.setText(param);
        return inflate;
    }

}
