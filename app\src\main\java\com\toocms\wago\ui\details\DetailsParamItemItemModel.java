package com.toocms.wago.ui.details;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.wago.bean.ProductDetailBean;
import com.toocms.wago.config.Constants;

public class DetailsParamItemItemModel extends MultiItemViewModel<DetailsModel> {

    public ObservableField<String> specName = new ObservableField<>();
    public ObservableField<String> value = new ObservableField<>();

    public DetailsParamItemItemModel(@NonNull DetailsModel viewModel, ProductDetailBean.ListBean.ValueListBean valueListBean) {
        super(viewModel);
        specName.set(valueListBean.specName);
        value.set(valueListBean.value);
    }

    public DetailsParamItemItemModel(@NonNull DetailsModel viewModel, ProductDetailBean.ListBean._ListBean.ValueListBean valueListBean) {
        super(viewModel);
        specName.set(valueListBean.specName);
        value.set(valueListBean.value);
    }

    @Override
    public String getItemType() {
        return Constants.RECYCLER_VIEW_ITEM_TYPE_ONE;
    }
}
