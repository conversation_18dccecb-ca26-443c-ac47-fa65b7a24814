package com.toocms.wago.ui.details;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.ProductDetailBean;
import com.toocms.wago.config.Constants;

public class DetailsParamItemItemModel2 extends MultiItemViewModel<DetailsModel> {

    public ObservableField<String> specName = new ObservableField<>();
    public ObservableBoolean isSelected = new ObservableBoolean();

    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {
        if (item instanceof DetailsParamItemItemModel) {
            binding.set(BR.detailsParamItemItemModel, R.layout.listitem_details_param_item);
        }
    });

    public DetailsParamItemItemModel2(@NonNull DetailsModel viewModel, ProductDetailBean.ListBean._ListBean listBean) {
        super(viewModel);
        specName.set(listBean.gropuName);
        CollectionUtils.forAllDo(listBean.valueList, (index, item) -> {
            items.add(new DetailsParamItemItemModel(viewModel, item));
        });
    }

    @Override
    public String getItemType() {
        return Constants.RECYCLER_VIEW_ITEM_TYPE_TWO;
    }

    public BindingCommand onTitleClickBindingCommand = new BindingCommand(() -> {
        isSelected.set(!isSelected.get());
    });
}
