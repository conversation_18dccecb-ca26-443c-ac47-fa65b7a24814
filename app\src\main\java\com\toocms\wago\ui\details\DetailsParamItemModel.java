package com.toocms.wago.ui.details;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.ProductDetailBean;
import com.toocms.wago.config.Constants;

public class DetailsParamItemModel extends MultiItemViewModel<DetailsModel> {

    public ObservableField<String> name = new ObservableField<>();
    public ObservableField<Boolean> isSelected = new ObservableField<>();
    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {
        if (item instanceof DetailsParamItemItemModel) {
            binding.set(BR.detailsParamItemItemModel, R.layout.listitem_details_param_item);
        } else if (item instanceof DetailsParamItemItemModel2) {
            binding.set(BR.detailsParamItemItemModel2, R.layout.listitem_details_param_item2);
        }
    });

    public BindingCommand onTitleClickBindingCommand = new BindingCommand(() -> {
        isSelected.set(!isSelected.get());
    });

    public DetailsParamItemModel(@NonNull DetailsModel viewModel, ProductDetailBean.ListBean listBean) {
        super(viewModel);
        isSelected.set(false);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_THREE);
        name.set(listBean.gropuName);
        if (CollectionUtils.isEmpty(listBean.list)) {
            CollectionUtils.forAllDo(listBean.valueList, (index, item) -> {
                items.add(new DetailsParamItemItemModel(viewModel, item));
            });
        } else
            CollectionUtils.forAllDo(listBean.list, (index, item) -> {
                items.add(new DetailsParamItemItemModel2(viewModel, item));
            });
    }
}
