package com.toocms.wago.ui.details;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.photoview.PhotoViewFgt;

import java.util.ArrayList;
import java.util.List;

public class DetailsPreviewImageItemModel extends ItemViewModel<DetailsModel> {

    public ArrayList<String> images = new ArrayList<>();
    public ObservableField<String> url = new ObservableField<>();

    public DetailsPreviewImageItemModel(@NonNull DetailsModel viewModel, ArrayList<String> list, String url) {
        super(viewModel);
        this.url.set(url);
        images = list;
    }

    public BindingCommand onItemClickBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putStringArrayList(Constants.KEY_IMAGES, images);
        bundle.putInt(Constants.KEY_POSITION, images.indexOf(url.get()));
        viewModel.startFragment(PhotoViewFgt.class, bundle);
    });
}
