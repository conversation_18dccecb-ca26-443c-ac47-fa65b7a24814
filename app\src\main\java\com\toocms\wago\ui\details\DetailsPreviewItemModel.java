package com.toocms.wago.ui.details;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.config.Constants;

import java.util.ArrayList;
import java.util.List;

public class DetailsPreviewItemModel extends MultiItemViewModel<DetailsModel> {

    public ObservableArrayList<DetailsPreviewImageItemModel> items = new ObservableArrayList<>();
    public ItemBinding<DetailsPreviewImageItemModel> itemBinding = ItemBinding.of(BR.detailsPreviewImageItemModel, R.layout.listitem_details_preview_image);

    public DetailsPreviewItemModel(@NonNull DetailsModel viewModel, List<String> list) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_TWO);
        CollectionUtils.forAllDo(list, (index, item) -> {
            items.add(new DetailsPreviewImageItemModel(viewModel, (ArrayList<String>) list, item));
        });
    }
}
