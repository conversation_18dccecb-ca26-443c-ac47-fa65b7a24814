package com.toocms.wago.ui.download_material;

import android.graphics.Color;
import android.view.View;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.StringUtils;
import com.qmuiteam.qmui.widget.tab.QMUITab;
import com.qmuiteam.qmui.widget.tab.QMUITabBuilder;
import com.qmuiteam.qmui.widget.tab.QMUITabIndicator;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.LanguageBean;
import com.toocms.wago.bean.ProductManualCategoryBean;
import com.toocms.wago.databinding.FgtDownloadMaterialBinding;
import com.toocms.wago.dialog.option.OptionDialog;
import com.toocms.wago.dialog.option.adt.OptionAdt;

import java.util.List;

public class DownloadMaterialFgt extends BaseFragment<FgtDownloadMaterialBinding, DownloadMaterialModel> {

    private OptionDialog<ProductManualCategoryBean> productOptionDialog;
    private OptionDialog<LanguageBean> languageOptionDialog;

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
        QMUITabBuilder builder = binding.tabSegment.tabBuilder();
        QMUITab tab1 = builder.setText("认证证书")
                .setTextSize(ConvertUtils.sp2px(15), ConvertUtils.sp2px(15))
                .build(getContext());
        binding.tabSegment.addTab(tab1);
        QMUITab tab2 = builder.setText("产品手册")
                .setTextSize(ConvertUtils.sp2px(15), ConvertUtils.sp2px(15))
                .build(getContext());
        binding.tabSegment.addTab(tab2);
        binding.tabSegment.setIndicator(new QMUITabIndicator(ConvertUtils.dp2px(3), false, false));
        binding.tabSegment.setOnTabClickListener((tabView, index) -> {
            viewModel.tab = index;
            switch (index) {
                case 0:
                    viewModel.onCertificateTabClick();
                    break;
                case 1:
                    viewModel.onProductTabClick();
                    break;
            }
            return false;
        });
        binding.tabSegment.notifyDataChanged();
        binding.tabSegment.selectTab(0);

        binding.typeFl.setOnClickListener(v -> {
            productOptionDialog.show(getChildFragmentManager(), "");
        });

        binding.languageFl.setOnClickListener(v -> {
            languageOptionDialog.show(getChildFragmentManager(), "");
        });
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_download_material;
    }

    @Override
    public int getVariableId() {
        return BR.downloadMaterialModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());

        viewModel.productEvent.observe(this, v -> productOptionDialog = new OptionDialog<>(R.layout.listitem_download_material_filter, new OptionDialog.Callback<ProductManualCategoryBean>() {
            @Override
            public void onBindItem(OptionAdt.Holder holder, ProductManualCategoryBean data, boolean isSelected) {
                holder.setText(R.id.item_tv, data.categoryName + "（" + data.count + "）");
                holder.viewSelected(isSelected);
            }

            @Override
            public void onSelectedItems(List<ProductManualCategoryBean> items) {
                if (CollectionUtils.isEmpty(items)) return;
                viewModel.categorySelectedPosition = viewModel.productBeans.indexOf(items.get(0));
                LogUtils.e(viewModel.categorySelectedPosition);
                viewModel.p = 1;
                if (StringUtils.isEmpty(items.get(0).certificateCategoryId)) {
                    viewModel.categoryId = items.get(0).productManualCategoryId;
                    viewModel.selectProductManualAPP(true);
                } else {
                    viewModel.categoryId = items.get(0).certificateCategoryId;
                    viewModel.selectCertificateAPP(true);
                }
            }
        }).setMaxSelectedCount(1)
                .setOptionItems(viewModel.productBeans)
//                .setSelectedItems(viewModel.categorySelectedPosition)
                .targetView(binding.typeFl));

        viewModel.languageEvent.observe(this, v -> languageOptionDialog = new OptionDialog<>(R.layout.listitem_download_material_filter, new OptionDialog.Callback<LanguageBean>() {
            @Override
            public void onBindItem(OptionAdt.Holder holder, LanguageBean data, boolean isSelected) {
                holder.setText(R.id.item_tv, data.language);
                holder.viewSelected(isSelected);
            }

            @Override
            public void onSelectedItems(List<LanguageBean> items) {
                if (CollectionUtils.isEmpty(items)) return;
                viewModel.languageSelectedPosition = viewModel.languageBeans.indexOf(items.get(0));
                viewModel.p = 1;
                if (StringUtils.isEmpty(items.get(0).certificateLanguageId)) {
                    viewModel.languageId = items.get(0).productManualLanguageId;
                    viewModel.selectProductManualAPP(true);
                } else {
                    viewModel.languageId = items.get(0).certificateLanguageId;
                    viewModel.selectCertificateAPP(true);
                }
            }
        }).setMaxSelectedCount(1)
                .setOptionItems(viewModel.languageBeans)
//                .setSelectedItems(viewModel.languageSelectedPosition)
                .targetView(binding.languageFl));
    }
}
