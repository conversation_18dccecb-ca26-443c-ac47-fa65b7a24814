package com.toocms.wago.ui.download_material;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ProductManualBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

public class DownloadMaterialFileItemModel extends ItemViewModel {

    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public ObservableField<String> imgUrl = new ObservableField<>();
    public ObservableField<String> fileName = new ObservableField<>();
    public String id;
    private boolean isCertificate;

    public BindingCommand onItemClickBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("detailType", isCertificate ? Constants.DETAIL_TYPE_CERTIFICATE_FILE : Constants.DETAIL_TYPE_MATERIAL_FILE);
        bundle.putString("productId", id);
        bundle.putString("productName", title.get());
        viewModel.startFragment(DetailsFgt.class, bundle);
    });

    public DownloadMaterialFileItemModel(@NonNull BaseViewModel viewModel, ProductManualBean.RowsBean rowsBean) {
        super(viewModel);
        isCertificate = StringUtils.isEmpty(rowsBean.productManualId);
        id = isCertificate ? rowsBean.certificateId : rowsBean.productManualId;
        title.set(rowsBean.title);
        subTitle.set(rowsBean.subTitle);
        imgUrl.set(rowsBean.imgUrl);
        fileName.set(isCertificate ? rowsBean.certificateFileName : rowsBean.productManualFileName);
    }
}
