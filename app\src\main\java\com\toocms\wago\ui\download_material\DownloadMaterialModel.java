package com.toocms.wago.ui.download_material;

import android.app.Application;
import android.view.KeyEvent;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.LanguageBean;
import com.toocms.wago.bean.ProductManualCategoryBean;
import com.toocms.wago.bean.ProductManualBean;

import java.util.List;

public class DownloadMaterialModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> queryString = new ObservableField<>();

    public int p = 1;
    public int tab = 0;
    public int categorySelectedPosition;
    public int languageSelectedPosition;
    public String categoryId;
    public String languageId;
    public List<ProductManualCategoryBean> productBeans;
    public List<LanguageBean> languageBeans;

    public SingleLiveEvent<Void> productEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> languageEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<DownloadMaterialFileItemModel> items = new ObservableArrayList<>();
    public ItemBinding<DownloadMaterialFileItemModel> itemBinding = ItemBinding.of(BR.downloadMaterialFileItemModel, R.layout.listitem_download_material_file);

    public DownloadMaterialModel(@NonNull Application application) {
        super(application);
        onCertificateTabClick();
    }

    public void onCertificateTabClick() {
        p = 1;
        categoryId = "";
        languageId = "";
        selectCertificateCategoryAPP();
        selectEnabledCertificateLanguage();
        selectCertificateAPP(true);
    }

    public void onProductTabClick() {
        p = 1;
        categoryId = "";
        languageId = "";
        selectProductManualCategoryAPP();
        selectEnabledProductManualLanguage();
        selectProductManualAPP(true);
    }

    public BindingCommand<Integer> onKey = new BindingCommand<>(keyCode -> {
        if (keyCode == KeyEvent.KEYCODE_ENTER)
            selectProductManualAPP(true);
    });

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        switch (tab) {
            case 0:
                categoryId = "";
                languageId = "";
                selectCertificateCategoryAPP();
                selectEnabledCertificateLanguage();
                selectCertificateAPP(false);
                break;
            case 1:
                categoryId = "";
                languageId = "";
                selectProductManualCategoryAPP();
                selectEnabledProductManualLanguage();
                selectProductManualAPP(false);
                break;
        }
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        switch (tab) {
            case 0:
                selectCertificateAPP(false);
                break;
            case 1:
                selectProductManualAPP(false);
                break;
        }

    });

    private void selectProductManualCategoryAPP() {
        ApiTool.get("productManual/category/selectProductManualCategoryAPP")
                .asTooCMSResponseList(ProductManualCategoryBean.class)
                .withViewModel(this)
                .request(productBeans -> {
                    this.productBeans = productBeans;
                    productEvent.call();
                });
    }

    public void selectEnabledProductManualLanguage() {
        ApiTool.get("productManual/language/selectEnabledProductManualLanguage")
                .asTooCMSResponseList(LanguageBean.class)
                .withViewModel(this)
                .request(languageBeans -> {
                    this.languageBeans = languageBeans;
                    languageEvent.call();
                });
    }

    public void selectProductManualAPP(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("categoryId", categoryId);
        query.addProperty("languageId", languageId);
        query.addProperty("queryString", queryString.get());
        ApiTool.postJson("productManual/manual/selectProductManualAPP")
                .add("currentPage", p)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(ProductManualBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(productManualBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(productManualBean.rows, (index, item) -> {
                        items.add(new DownloadMaterialFileItemModel(this, item));
                    });
                });
    }

    public void selectCertificateCategoryAPP() {
        ApiTool.get("certificate/certificateCategory/selectCertificateCategoryAPP")
                .asTooCMSResponseList(ProductManualCategoryBean.class)
                .withViewModel(this)
                .request(productBeans -> {
                    this.productBeans = productBeans;
                    productEvent.call();
                });
    }

    public void selectEnabledCertificateLanguage() {
        ApiTool.get("certificate/certificateLanguage/selectEnabledCertificateLanguage")
                .asTooCMSResponseList(LanguageBean.class)
                .withViewModel(this)
                .request(languageBeans -> {
                    this.languageBeans = languageBeans;
                    languageEvent.call();
                });
    }

    public void selectCertificateAPP(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("categoryId", categoryId);
        query.addProperty("languageId", languageId);
        query.addProperty("queryString", queryString.get());
        ApiTool.postJson("certificate/selectCertificateAPP")
                .add("currentPage", p)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(ProductManualBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(productManualBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(productManualBean.rows, (index, item) -> {
                        items.add(new DownloadMaterialFileItemModel(this, item));
                    });
                });
    }
}
