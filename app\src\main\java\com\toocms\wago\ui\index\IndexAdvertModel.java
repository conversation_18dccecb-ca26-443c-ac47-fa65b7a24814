package com.toocms.wago.ui.index;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableList;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.widget.banner.BannerItem;
import com.toocms.tab.widget.banner.base.BaseBanner;
import com.toocms.wago.bean.BannerBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;
import com.toocms.wago.ui.web.WebFgt;

import java.util.List;

public class IndexAdvertModel extends MultiItemViewModel<IndexModel> {

    public ObservableList<BannerItem> bannerItems = new ObservableArrayList<>();

    public IndexAdvertModel(@NonNull IndexModel viewModel, List<BannerBean> list) {
        super(viewModel);
        CollectionUtils.forAllDo(list, (index, item) -> {
            BannerItem bannerItem = new BannerItem();
            bannerItem.imgUrl = item.imgUrl;
            bannerItem.target_rule = String.valueOf(item.skipWay);
            bannerItem.expand1 = item.productId;    // 商品id
            bannerItem.expand2 = item.skipUrl;  // 网址
            bannerItems.add(bannerItem);
        });
    }

    @Override
    public String getItemType() {
        return Constants.RECYCLER_VIEW_ITEM_TYPE_TWO;
    }

    public BaseBanner.OnItemClickListener<BannerItem> onItemClickListener = (view, item, position) -> {
        Bundle bundle = new Bundle();
        switch (item.target_rule) {
            case "1":   // 跳商品
                bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
                bundle.putString("productId", item.expand1);
                viewModel.startFragment(DetailsFgt.class, bundle);
                break;
            case "2":   // 跳网页
                bundle.putString("url", item.expand2);
                viewModel.startFragment(WebFgt.class, bundle);
                break;
            default:
                break;
        }
    };
}
