package com.toocms.wago.ui.index;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.recyclerview.widget.GridLayoutManager;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.BannerBean;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.bean.ScadaBean;
import com.toocms.wago.bean.ShouPlanBean;
import com.toocms.wago.bean.User;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.search.SearchFgt;

public class IndexModel extends BaseViewModel<BaseModel> {

    private int position;
    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();

    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {
        if (item instanceof IndexUserItemModel) {
            binding.set(BR.indexUserItemModel, R.layout.layout_index_user);
        } else if (item instanceof IndexAdvertModel) {
            binding.set(BR.indexAdvertModel, R.layout.layout_index_advert);
        } else if (item instanceof IndexTitleModel) {
            binding.set(BR.indexTitleModel, R.layout.layout_index_title);
        } else if (item instanceof IndexProductItemModel) {
            binding.set(BR.indexProductItemModel, R.layout.layout_index_product);
        }
    });

    public GridLayoutManager.SpanSizeLookup spanSizeLookup = new GridLayoutManager.SpanSizeLookup() {
        @Override
        public int getSpanSize(int position) {
            int result = 0;
            MultiItemViewModel item = items.get(position);
            if (item instanceof IndexUserItemModel) {
                result = 2;
            } else if (item instanceof IndexAdvertModel) {
                result = 2;
            } else if (item instanceof IndexTitleModel) {
                result = 2;
            } else if (item instanceof IndexProductItemModel) {
                result = 1;
            }
            return result;
        }
    };

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        selectEnabledBanner(false);
    });

    public BindingCommand onSearchClickBindingCommand = new BindingCommand(() -> {
        startFragment(SearchFgt.class, null);
    });

    public IndexModel(@NonNull Application application) {
        super(application);
        selectEnabledBanner(true);
    }

    private void selectEnabledBanner(boolean isShowLoading) {
        ApiTool.post("system/banner/selectEnabledBanner")
                .asTooCMSResponseList(BannerBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(bannerBeans -> {
                    items.clear();
                    items.add(new IndexUserItemModel(this));
                    items.add(new IndexAdvertModel(this, bannerBeans));
                    items.add(new IndexTitleModel(this, StringUtils.getString(R.string.str_product_list)));
                    selectByProductClass(isShowLoading);
                });
    }

    private void selectByProductClass(boolean isShowLoading) {
        ApiTool.get("product/selectByProductClass")
                .asTooCMSResponseList(ProductClassBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(productClassBeans -> {
                    CollectionUtils.forAllDo(productClassBeans, (index, item) -> {
                        position = index;
                        items.add(new IndexProductItemModel(this, item, index));
                    });
                    selectShouPlan();
                });
    }

    private void selectShouPlan() {
        ApiTool.get("pPlan/selectShouPlan")
                .asTooCMSResponse(ShouPlanBean.class)
                .withViewModel(this)
                .request(shouPlanBean -> {
                    items.add(new IndexProductItemModel(this, shouPlanBean, ++position));
//                    ScadaBean scadaBean = new ScadaBean();
//                    scadaBean.url = "http://wagoapp.oss-cn-beijing.aliyuncs.com/img/20210709095008125.png";
//                    scadaBean.title = "WAGO SCADA";
//                    items.add(new IndexProductItemModel(this, scadaBean, ++position));
                    onRefreshFinish.call();
                });
    }
}
