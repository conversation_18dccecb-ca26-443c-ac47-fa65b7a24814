package com.toocms.wago.ui.index;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.bean.ScadaBean;
import com.toocms.wago.bean.ShouPlanBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.module.ModuleFgt;
import com.toocms.wago.ui.web.WebFgt;

public class IndexProductItemModel extends MultiItemViewModel<IndexModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> product = new ObservableField<>();
    public ObservableField<Integer> position = new ObservableField<>();
    public String categoryId;
    private boolean isProduct;

    public BindingCommand onItemClickBindingCommand = new BindingCommand(() -> {
        if (StringUtils.isEmpty(categoryId)) {
            Bundle bundle = new Bundle();
            bundle.putString("url", "http://www.scadaforweb.com");
            viewModel.startFragment(WebFgt.class, bundle);
//            Intent intent = new Intent();
//            intent.setAction(Intent.ACTION_VIEW);
//            intent.setData(Uri.parse("http://www.scadaforweb.com"));
//            ActivityUtils.startActivity(Intent.createChooser(intent, "请选择浏览器"));
        } else {
            Bundle bundle = new Bundle();
            bundle.putBoolean("isProduct", isProduct);
            bundle.putString("categoryId", categoryId);
            bundle.putString("product", product.get());
            viewModel.startFragment(ModuleFgt.class, bundle);
        }
    });

    public IndexProductItemModel(@NonNull IndexModel viewModel, ProductClassBean productClassBean, int position) {
        super(viewModel);
        isProduct = true;
        this.position.set(position);
        categoryId = productClassBean.productCategoryId;
        url.set(productClassBean.imgurl);
        product.set(productClassBean.productCategoryName);
    }

    public IndexProductItemModel(@NonNull IndexModel viewModel, ShouPlanBean shouPlanBean, int position) {
        super(viewModel);
        isProduct = false;
        this.position.set(position);
        categoryId = shouPlanBean.productPlanCategoryId;
        url.set(shouPlanBean.imgUrl);
        product.set(shouPlanBean.name);
    }

    public IndexProductItemModel(@NonNull IndexModel viewModel, ScadaBean scadaBean, int position) {
        super(viewModel);
        url.set(scadaBean.url);
        product.set(scadaBean.title);
        this.position.set(position);
    }

    @Override
    public String getItemType() {
        return Constants.RECYCLER_VIEW_ITEM_TYPE_FOUR;
    }
}
