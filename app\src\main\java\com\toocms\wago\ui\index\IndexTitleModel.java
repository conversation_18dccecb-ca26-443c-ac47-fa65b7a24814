package com.toocms.wago.ui.index;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.wago.config.Constants;

public class IndexTitleModel extends MultiItemViewModel<IndexModel> {
    public ObservableField<String> data=new ObservableField<>();
    public IndexTitleModel(@NonNull IndexModel viewModel,String title) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_TWO);
        this.data.set(title);
    }
}
