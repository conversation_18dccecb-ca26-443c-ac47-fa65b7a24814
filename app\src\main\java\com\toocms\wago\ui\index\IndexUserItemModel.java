package com.toocms.wago.ui.index;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.login.LoginFgt;
import com.toocms.wago.ui.mine.UserCenterFgt;
import com.toocms.wago.ui.mine.UserCenterModel;
import com.toocms.wago.ui.mine.my_share.MyShareFgt;
import com.toocms.wago.ui.mine.my_share.MyShareModel;

public class IndexUserItemModel extends MultiItemViewModel<IndexModel> {

    public ObservableField<String> head = new ObservableField<>();

    public BindingCommand onUserCenterClickBindingCommand = new BindingCommand(() -> {
        if (!UserRepository.getInstance().isLogin()) {
            viewModel.startFragment(LoginFgt.class);
            return;
        }
        viewModel.startFragment(UserCenterFgt.class);
    });

    public IndexUserItemModel(@NonNull IndexModel viewModel) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_ONE);
        head.set(UserRepository.getInstance().getUser().headImgUrl);
    }
}
