package com.toocms.wago.ui.line_shop;

import android.annotation.SuppressLint;
import android.view.KeyEvent;
import android.webkit.WebSettings;
import android.webkit.WebView;

import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtLineShopBinding;
import com.toocms.wago.ui.web.WebFgt;

public class LineShopFgt extends BaseFragment<FgtLineShopBinding, LineShopModel> {

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onFragmentCreated() {
        topBar.removeAllLeftViews();
        WebSettings settings = binding.webview.getSettings();
        settings.setJavaScriptEnabled(true);
        binding.webview.setWebViewClient(new AdvertWebViewClient());
        binding.webview.loadUrl("http://www.wago-mall.com");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_line_shop;
    }

    @Override
    public int getVariableId() {
        return BR.lineShopModel;
    }

    @Override
    protected void viewObserver() {

    }

    @Override
    protected void onBackPressed() {
        if (binding.webview.canGoBack()) {
            binding.webview.goBack();
        } else
            super.onBackPressed();
    }

    public class AdvertWebViewClient extends QMUIWebViewClient {

        public AdvertWebViewClient() {
            super(true, true);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            topBar.setTitle(view.getTitle());
        }
    }
}
