package com.toocms.wago.ui.line_shop;

import android.app.Application;
import android.content.Intent;
import android.net.Uri;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.IntentUtils;
import com.toocms.tab.base.BaseFragment;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;

public class LineShopModel extends BaseViewModel<BaseModel> {

    public LineShopModel(@NonNull Application application) {
        super(application);
    }

    public BindingCommand click = new BindingCommand(() -> {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_VIEW);
        intent.setData(Uri.parse("http://www.wago-mall.com"));
        ActivityUtils.startActivity(Intent.createChooser(intent, "请选择浏览器"));
    });
}
