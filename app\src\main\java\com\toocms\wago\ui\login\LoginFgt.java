package com.toocms.wago.ui.login;

import android.content.res.ColorStateList;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.text.InputType;
import android.view.View;
import android.view.inputmethod.EditorInfo;

import androidx.annotation.ColorInt;
import androidx.core.graphics.drawable.DrawableCompat;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.ColorUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.SpanUtils;
import com.toocms.tab.base.BaseFragment;
import com.toocms.tab.expand.agreement.AgreementDialog;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtLoginBinding;
import com.toocms.wago.ui.web.WebFgt;

public class LoginFgt extends BaseFragment<FgtLoginBinding, LoginModel> {
    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
        initializePasswordEdt();
        new AgreementDialog()
                .setPolicyClickListener(view -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("url", "https://www.app.hemajia.net/yinsitiaokuandl.html");
                    startFragment(WebFgt.class, bundle);
                })
                .setAgreementClickListener(view -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("url", "https://www.app.hemajia.net/yonghuxieyi.html");
                    startFragment(WebFgt.class, bundle);
                }).show(getChildFragmentManager(), "Agreement");
        SpanUtils.with(binding.loginAgreement)
                .append("同意")
                .setFontSize(12, true)
                .setForegroundColor(0xFF000000)
                .append("《用户协议》")
                .setFontSize(12, true)
                .setClickSpan(ColorUtils.getColor(R.color.clr_main), true, view -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("url", "https://www.app.hemajia.net/yonghuxieyi.html");
                    startFragment(WebFgt.class, bundle);
                })
                .append("和")
                .setFontSize(12, true)
                .setForegroundColor(0xFF000000)
                .append("《隐私政策》")
                .setFontSize(12, true)
                .setClickSpan(ColorUtils.getColor(R.color.clr_main), true, view -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("url", "https://www.app.hemajia.net/yinsitiaokuandl.html");
                    startFragment(WebFgt.class, bundle);
                })
                .create();
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_login;
    }

    @Override
    public int getVariableId() {
        return BR.loginModel;
    }

    @Override
    protected void viewObserver() {

    }

    /**
     * 初始化密码输入框
     * 1.限制内容的显示范围
     * 2.添加查看和隐藏密码功能
     */
    private void initializePasswordEdt() {
        if (null == binding.passwordEdt) return;
        if (null == binding.showOrHintIv) return;
        //限制内容的显示范围
        binding.showOrHintIv.post(() -> {
            binding.passwordEdt.setPadding(ConvertUtils.dp2px(10), ConvertUtils.dp2px(10), binding.showOrHintIv.getWidth(), ConvertUtils.dp2px(10));
        });
        //初始化密码显示和隐藏按钮的资源颜色
        changeShowOrHintIvDrawableColor(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD == (binding.passwordEdt.getInputType() & InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD));
        //添加查看和隐藏密码功能
        binding.showOrHintIv.setOnClickListener(v -> {
            //获取原光标选择区域
            int selectionStart = binding.passwordEdt.getSelectionStart();
            int selectionEnd = binding.passwordEdt.getSelectionEnd();
            //改变密码的可见性
            int inputType = binding.passwordEdt.getInputType();
            if (InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD != (inputType & InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD)) {
                binding.passwordEdt.setInputType(InputType.TYPE_CLASS_TEXT | InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD);
            } else {
                binding.passwordEdt.setInputType(InputType.TYPE_CLASS_TEXT | EditorInfo.TYPE_TEXT_VARIATION_PASSWORD);
            }
            //修改密码显示和隐藏按钮的资源颜色
            changeShowOrHintIvDrawableColor(InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD == (binding.passwordEdt.getInputType() & InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD));
            //回复原光标选择区域
            binding.passwordEdt.setSelection(selectionStart, selectionEnd);
        });
    }

    /**
     * 改变密码显示和隐藏按钮的资源颜色
     */
    private void changeShowOrHintIvDrawableColor(boolean isVisible) {
        Drawable drawable = binding.showOrHintIv.getDrawable().mutate();
        DrawableCompat.setTintList(drawable, ColorStateList.valueOf(isVisible ? ColorUtils.getColor(R.color.clr_main) : 0xFF000000));
    }


}
