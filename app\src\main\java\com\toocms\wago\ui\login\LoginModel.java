package com.toocms.wago.ui.login;

import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.share.TabShare;
import com.toocms.tab.share.listener.OnAuthListener;
import com.toocms.tab.share.login.PlatformUser;
import com.toocms.wago.bean.User;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.login.forget_password.ForgetPasswordFgt;
import com.toocms.wago.ui.login.register.RegisterFgt;
import com.toocms.wago.ui.login.verification.VerificationPhoneFgt;
import com.toocms.wago.ui.login.verify_code_login.VerifyCodeLoginFgt;
import com.toocms.wago.ui.main.MainFgt;
import com.umeng.socialize.bean.SHARE_MEDIA;

public class LoginModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> username = new ObservableField<>();
    public ObservableField<String> password = new ObservableField<>();
    public ObservableBoolean agreement = new ObservableBoolean();

    /**
     * 点击事件
     */
    //登录点击
    public BindingCommand onLoginClickBindingCommand = new BindingCommand(this::loginUser4Username);

    //忘记密码点击事件
    public BindingCommand onForgetPasswordClickBindingCommand = new BindingCommand(() -> {
        startFragment(ForgetPasswordFgt.class);
    });

    //验证码登录点击事件
    public BindingCommand onVerifyCodeLoginClickBindingCommand = new BindingCommand(() -> {
        startFragment(VerifyCodeLoginFgt.class);
    });

    //注册点击事件
    public BindingCommand onRegisterClickBindingCommand = new BindingCommand(() -> {
        startFragment(RegisterFgt.class);
    });

    //微信登录点击事件
    public BindingCommand onWXClickBindingCommand = new BindingCommand(() -> {
        if (!agreement.get()) {
            showToast("请先阅读并同意用户协议以及隐私政策");
            return;
        }
        TabShare.getOneKeyLogin().showUser(true, SHARE_MEDIA.WEIXIN, new OnAuthListener() {
            @Override
            public void onComplete(SHARE_MEDIA share_media, int action, PlatformUser user) {
                AndroidLoginWX(user.getOpenId(), user.getName(), user.getGender(), user.getHead(), user.getToken());
            }
        });
    });

    //游客访问
    public BindingCommand onTouristsClickBindingCommand = new BindingCommand(() -> {
        UserRepository.getInstance().setTouristsLogin(true);
        startFragment(MainFgt.class, true);
    });

    public LoginModel(@NonNull Application application) {
        super(application);
        Messenger.getDefault().register(this, Constants.MESSENGER_TOKEN_FINISH_LOGIN, this::finishFragment);
    }

    private void loginUser4Username() {
        if (!agreement.get()) {
            showToast("请先阅读并同意用户协议以及隐私政策");
            return;
        }
        if (StringUtils.isEmpty(username.get())) {
            showToast("请输入账号");
            return;
        }
        if (StringUtils.isEmpty(password.get())) {
            showToast("请输入密码");
            return;
        }
        ApiTool.get("user/user/loginUser4Username")
                .add("username", username.get())
                .add("password", password.get())
                .add("type", 1)
                .asTooCMSResponse(User.class)
                .withViewModel(this)
                .request(user -> {
                    UserRepository.getInstance().setLogin(true);
                    UserRepository.getInstance().setUser(user);
                    startFragment(MainFgt.class, true);
                });
    }

    private void AndroidLoginWX(String openid, String name, String gender, String head, String token) {
        ApiTool.postJson("user/user/AndroidLoginWX")
                .add("openId", openid)
                .add("name", name)
                .add("gender", gender)
                .add("head", head)
                .add("token", token)
                .add("type", 1)
                .asTooCMSResponse(User.class)
                .withViewModel(this)
                .request(user -> {
                    if (StringUtils.isEmpty(user.phone)) {   // 未绑定手机号
                        UserRepository.getInstance().setUser(user);
                        startFragment(VerificationPhoneFgt.class);
                    } else {    // 已绑定手机号
                        UserRepository.getInstance().setLogin(true);
                        UserRepository.getInstance().setUser(user);
                        startFragment(MainFgt.class, true);
                    }
                });
    }
}
