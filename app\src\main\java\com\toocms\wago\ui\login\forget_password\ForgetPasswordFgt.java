package com.toocms.wago.ui.login.forget_password;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtForgetPasswordBinding;

public class ForgetPasswordFgt extends BaseFragment<FgtForgetPasswordBinding, ForgetPasswordModel> {
    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_forget_password;
    }

    @Override
    public int getVariableId() {
        return BR.forgetPasswordModel;
    }

    @Override
    protected void viewObserver() {

    }
}
