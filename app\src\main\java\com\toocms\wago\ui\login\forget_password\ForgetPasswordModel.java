package com.toocms.wago.ui.login.forget_password;

import android.app.Application;

import androidx.annotation.NonNull;

import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;

public class ForgetPasswordModel extends BaseViewModel<BaseModel> {

    public BindingCommand onBackClickBindingCommand=new BindingCommand(()->{
        finishFragment();
    });

    public ForgetPasswordModel(@NonNull Application application) {
        super(application);
    }
}
