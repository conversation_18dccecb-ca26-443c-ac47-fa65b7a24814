package com.toocms.wago.ui.login.register;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtRegisterBinding;

public class RegisterFgt extends BaseFragment<FgtRegisterBinding,RegisterModel> {
    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_register;
    }

    @Override
    public int getVariableId() {
        return BR.registerModel;
    }

    @Override
    protected void viewObserver() {

    }
}
