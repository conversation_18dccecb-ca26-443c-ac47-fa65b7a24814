package com.toocms.wago.ui.login.register;

import android.app.Application;
import android.os.CountDownTimer;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.network.RxHttp;
import com.toocms.wago.R;
import com.toocms.wago.bean.HotSearchBean;
import com.toocms.wago.ui.mine.leave_word.LeaveWordFgt;
import com.toocms.wago.ui.search.SearchFloatModel;
import com.toocms.wago.ui.search.SearchTitleModel;

import java.util.ArrayList;
import java.util.List;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;

public class RegisterModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> username = new ObservableField<>();
    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> code = new ObservableField<>();
    public ObservableField<String> password = new ObservableField<>();
    public ObservableField<String> countdown = new ObservableField<>("获取验证码");
    public ObservableBoolean clickable = new ObservableBoolean(true);
    private long totalTime = 60;

    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public BindingCommand onRegisterBindingCommand = new BindingCommand(() -> {
//        startFragment(LeaveWordFgt.class);
        registerUser();
    });

    public BindingCommand onCodeBindingCommand = new BindingCommand(this::call);

    public RegisterModel(@NonNull Application application) {
        super(application);
    }

    private void registerUser() {
        ApiTool.postJson("user/user/registerUser")
                .add("username", username.get())
                .add("phone", phone.get())
                .add("code", code.get())
                .add("password", password.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    finishFragment();
                });
    }

    private void getVerify() {
        ApiTool.get("user/user/sendRegisterVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(this::showToast);
    }

    public CountDownTimer timer = new CountDownTimer(totalTime * 1000, 1000) {

        @Override
        public void onTick(long l) {
            countdown.set("重新获取(" + l / 1000 + "s)");
        }

        @Override
        public void onFinish() {
            countdown.set("获取验证码");
            clickable.set(true);
        }
    };

    private void call() {
        if (StringUtils.isEmpty(phone.get())) {
            showToast("请输入手机号");
            return;
        }
        clickable.set(false);
        timer.start();
        getVerify();
    }
}
