package com.toocms.wago.ui.login.verification;

import android.view.View;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtVerificationPhoneBinding;

/**
 * Description :
 * Author : Zero
 * Date : 2021/10/18
 */
public class VerificationPhoneFgt extends BaseFragment<FgtVerificationPhoneBinding, VerificationPhoneViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_verification_phone;
    }

    @Override
    public int getVariableId() {
        return BR.verificationPhoneViewModel;
    }

    @Override
    protected void viewObserver() {

    }
}
