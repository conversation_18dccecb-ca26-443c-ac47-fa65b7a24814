package com.toocms.wago.ui.login.verification;

import android.app.Application;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.FragmentUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.main.MainFgt;

import org.jetbrains.annotations.NotNull;

/**
 * Description :
 * Author : Zero
 * Date : 2021/10/18
 */
public class VerificationPhoneViewModel extends BaseViewModel {

    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> code = new ObservableField<>();
    public ObservableField<String> countdown = new ObservableField<>("获取验证码");
    public ObservableBoolean clickable = new ObservableBoolean(true);

    private String id;
    private long totalTime = 60;

    public VerificationPhoneViewModel(@NonNull @NotNull Application application) {
        super(application);
    }

    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public BindingCommand onRegisterBindingCommand = new BindingCommand(() -> {
//        startFragment(LeaveWordFgt.class);
        bindingPhone();
    });

    public BindingCommand onCodeBindingCommand = new BindingCommand(this::call);

    private void bindingPhone() {
        ApiTool.get("user/user/bindingPhone")
                .add("phone", phone.get())
                .add("code", code.get())
                .add("id", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    Messenger.getDefault().sendNoMsg(Constants.MESSENGER_TOKEN_FINISH_LOGIN);
                    UserRepository.getInstance().setLogin(true);
                    UserRepository.getInstance().setUserInfo("phone", phone.get());
                    showToast(s);
                    startFragment(MainFgt.class, true);
                });
    }

    private void getVerify() {
        ApiTool.get("user/user/sendBindingPhoneVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    clickable.set(false);
                    timer.start();
                });
    }

    public CountDownTimer timer = new CountDownTimer(totalTime * 1000, 1000) {

        @Override
        public void onTick(long l) {
            countdown.set("重新获取(" + l / 1000 + "s)");
        }

        @Override
        public void onFinish() {
            countdown.set("获取验证码");
            clickable.set(true);
        }
    };

    private void call() {
        if (StringUtils.isEmpty(phone.get())) {
            showToast("请输入手机号");
            return;
        }
        getVerify();
    }
}
