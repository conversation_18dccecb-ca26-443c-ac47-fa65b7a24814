package com.toocms.wago.ui.login.verify_code_login;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtCheckLoginVerifyCodeBinding;

public class CheckLoginVerifyCodeFgt extends BaseFragment<FgtCheckLoginVerifyCodeBinding, CheckLoginVerifyCodeModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
        viewModel.phone.set(getArguments().getString("phone"));
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_check_login_verify_code;
    }

    @Override
    public int getVariableId() {
        return BR.checkLoginVerifyCodeModel;
    }

    @Override
    protected void viewObserver() {

    }
}
