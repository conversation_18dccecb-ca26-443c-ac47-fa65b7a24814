package com.toocms.wago.ui.login.verify_code_login;

import android.app.Application;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.bean.User;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.main.MainFgt;

public class CheckLoginVerifyCodeModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> code = new ObservableField<>();
    public ObservableField<String> countdown = new ObservableField<>("");
    public ObservableBoolean clickable = new ObservableBoolean(true);
    private long totalTime = 60;

    public BindingCommand onBackClickBindingCommand = new BindingCommand(this::finishFragment);

    public BindingCommand onLoginClickBindingCommand = new BindingCommand(this::loginUser4Phone);

    public CheckLoginVerifyCodeModel(@NonNull Application application) {
        super(application);
        call();
    }

    public CountDownTimer timer = new CountDownTimer(totalTime * 1000, 1000) {

        @Override
        public void onTick(long l) {
            countdown.set(l / 1000 + "秒后重新获取验证码 ");
        }

        @Override
        public void onFinish() {
            countdown.set("");
            clickable.set(true);
        }
    };

    private void call() {
        clickable.set(false);
        timer.start();
    }

    private void loginUser4Phone() {
        if (StringUtils.isEmpty(code.get())) {
            showToast("请输入验证码");
            return;
        }
        ApiTool.get("user/user/loginUser4Phone")
                .add("phone", phone.get())
                .add("code", code.get())
                .add("type", 1)
                .asTooCMSResponse(User.class)
                .withViewModel(this)
                .request(user -> {
                    Messenger.getDefault().sendNoMsg(Constants.MESSENGER_TOKEN_FINISH_LOGIN);
                    UserRepository.getInstance().setLogin(true);
                    UserRepository.getInstance().setUser(user);
                    startFragment(MainFgt.class, true);
                });
    }
}
