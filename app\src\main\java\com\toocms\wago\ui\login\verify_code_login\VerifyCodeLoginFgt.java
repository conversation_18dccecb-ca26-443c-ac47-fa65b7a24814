package com.toocms.wago.ui.login.verify_code_login;

import android.view.View;

import com.blankj.utilcode.util.ConvertUtils;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtVerifyCodeLoginBinding;

public class VerifyCodeLoginFgt extends BaseFragment<FgtVerifyCodeLoginBinding, VerifyCodeLoginModel> {
    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
        initializePhoneEdit();
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_verify_code_login;
    }

    @Override
    public int getVariableId() {
        return BR.verifyCodeLoginModel;
    }

    @Override
    protected void viewObserver() {

    }

    /**
     * 初始化手机号输入框
     */
    private void initializePhoneEdit() {
        if (null == binding.phoneEdt) return;
        if (null == binding.clearIv) return;
        binding.clearIv.post(() -> {
            binding.phoneEdt.setPadding(ConvertUtils.dp2px(10), ConvertUtils.dp2px(10), binding.clearIv.getWidth(), ConvertUtils.dp2px(10));
        });
        binding.clearIv.setOnClickListener(v -> {
            binding.phoneEdt.setText(null);
        });
    }
}
