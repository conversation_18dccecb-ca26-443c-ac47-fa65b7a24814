package com.toocms.wago.ui.login.verify_code_login;

import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;

public class VerifyCodeLoginModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> phone = new ObservableField<>();

    public BindingCommand onAcquireVerifyCodeClickBindingCommand = new BindingCommand(this::getVerify);

    public BindingCommand onBackClickBindingCommand = new BindingCommand(this::finishFragment);

    public VerifyCodeLoginModel(@NonNull Application application) {
        super(application);
    }

    private void getVerify() {
        if (StringUtils.isEmpty(phone.get())) {
            showToast("请输入手机号");
            return;
        }
        ApiTool.get("user/user/sendLoginVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("phone", phone.get());
                    startFragment(CheckLoginVerifyCodeFgt.class, bundle, true);
                });
    }
}
