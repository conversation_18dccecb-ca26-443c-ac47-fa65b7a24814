package com.toocms.wago.ui.main;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.BarUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.expand.tabsegment.BaseBottomTabSegmentFragment;
import com.toocms.tab.expand.tabsegment.TabSegmentItem;
import com.toocms.wago.R;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.about_us.AboutUsFgt;
import com.toocms.wago.ui.download_material.DownloadMaterialFgt;
import com.toocms.wago.ui.index.IndexFgt;
import com.toocms.wago.ui.line_shop.LineShopFgt;
import com.toocms.wago.ui.login.LoginFgt;
import com.toocms.wago.ui.newest_product.NewestProductFgt;
import com.toocms.wago.ui.product_libs.ProductLibsFgt;

public class MainFgt extends BaseBottomTabSegmentFragment {

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        if (!UserRepository.getInstance().isLogin() && !UserRepository.getInstance().isTouristsLogin()) {
            startFragment(LoginFgt.class, true);
        }
        return super.onCreateView(inflater, container, savedInstanceState);
    }

    @Override
    protected TabSegmentItem[] getTabSegmentItems() {
        return new TabSegmentItem[]{
                new TabSegmentItem(R.drawable.bitmap_index_normal
                        , R.drawable.bitmap_index_normal
                        , StringUtils.getString(R.string.str_index)
                        , IndexFgt.class)
                , new TabSegmentItem(R.drawable.bitmap_product_normal
                , R.drawable.bitmap_product_selected
                , StringUtils.getString(R.string.str_product_libs)
                , ProductLibsFgt.class)
                , new TabSegmentItem(R.drawable.bitmap_download_normal
                , R.drawable.bitmap_download_selected
                , StringUtils.getString(R.string.str_download_material)
                , DownloadMaterialFgt.class)
                , new TabSegmentItem(R.drawable.bitmap_shop_normal
                , R.drawable.bitmap_shop_selected
                , StringUtils.getString(R.string.str_line_shop)
                , LineShopFgt.class)
                , new TabSegmentItem(R.drawable.bitmap_mine_normal
                , R.drawable.bitmap_mine_selected
                , StringUtils.getString(R.string.str_about_us)
                , AboutUsFgt.class)
        };
    }

    @Override
    protected boolean isSwipeable() {
        return false;
    }

//    @Override
//    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
//        if (!UserRepository.getInstance().isLogin())
//            startFragment(LoginFgt.class, true);
//        return super.onCreateView(inflater, container, savedInstanceState);
//    }

}
