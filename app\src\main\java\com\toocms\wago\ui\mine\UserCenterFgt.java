package com.toocms.wago.ui.mine;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtUserCenterBinding;

public class UserCenterFgt extends BaseFragment<FgtUserCenterBinding, UserCenterModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_user_center;
    }

    @Override
    public int getVariableId() {
        return BR.userCenterModel;
    }

    @Override
    protected void viewObserver() {

    }
}
