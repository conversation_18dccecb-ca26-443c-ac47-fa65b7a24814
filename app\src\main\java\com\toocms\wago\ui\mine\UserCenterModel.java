package com.toocms.wago.ui.mine;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.luck.picture.lib.entity.LocalMedia;
import com.luck.picture.lib.listener.OnResultCallbackListener;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.mine.browse_record.BrowseRecordFgt;
import com.toocms.wago.ui.mine.download_record.DownloadRecordFgt;
import com.toocms.wago.ui.mine.leave_word.LeaveWordFgt;
import com.toocms.wago.ui.mine.my_collect.MyCollectFgt;
import com.toocms.wago.ui.mine.my_share.MyShareFgt;
import com.toocms.wago.ui.mine.settings.SettingsFgt;
import com.toocms.wago.ui.mine.system_message.SystemMessageFgt;

import java.io.File;
import java.util.List;

public class UserCenterModel extends BaseViewModel<BaseModel> {

    public ObservableBoolean isShowHeadFrame = new ObservableBoolean();
    public ObservableField<String> head = new ObservableField<>();
    public ObservableField<String> nickname = new ObservableField<>();

    public UserCenterModel(@NonNull Application application) {
        super(application);
    }

    @Override
    public void onResume() {
        super.onResume();
        head.set(UserRepository.getInstance().getUser().headImgUrl);
        nickname.set(UserRepository.getInstance().getUser().nickname);
    }

    public BindingCommand onSettingsClickBindingCommand = new BindingCommand(() -> {
        startFragment(SettingsFgt.class);
    });
    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });
    public BindingCommand onMyShareClickBindingCommand = new BindingCommand(() -> {
        startFragment(MyShareFgt.class);
    });
    public BindingCommand onMyCollectClickBindingCommand = new BindingCommand(() -> {
        startFragment(MyCollectFgt.class);
    });
    public BindingCommand onBrowseRecordClickBindingCommand = new BindingCommand(() -> {
        startFragment(BrowseRecordFgt.class);
    });
    public BindingCommand onDownloadRecordClickBindingCommand = new BindingCommand(() -> {
        startFragment(DownloadRecordFgt.class);
    });
    public BindingCommand onSystemMessageClickBindingCommand = new BindingCommand(() -> {
        startFragment(SystemMessageFgt.class);
    });
    public BindingCommand onUploadHeadClickBindingCommand = new BindingCommand(() -> {
        startSelectSignImageAty(new OnResultCallbackListener<LocalMedia>() {
            @Override
            public void onResult(List<LocalMedia> result) {
                updateHeadImgUrl(new File(result.get(0).getCutPath()));
            }

            @Override
            public void onCancel() {

            }
        });
    });

    public BindingCommand startLeaveWord = new BindingCommand(() -> {
        startFragment(LeaveWordFgt.class);
    });

    public BindingCommand onUploadHeadFrameClickBindingCommand = new BindingCommand(() -> {
        isShowHeadFrame.set(!isShowHeadFrame.get());
    });

    private void updateHeadImgUrl(File file) {
        ApiTool.post("user/user/updateHeadImgUrl")
                .addFile("file", file)
                .add("user_id", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    UserRepository.getInstance().setUserInfo("headImgUrl", "");
                    head.set(UserRepository.getInstance().getUser().headImgUrl);
                    onUploadHeadFrameClickBindingCommand.execute();
                });
    }
}
