package com.toocms.wago.ui.mine.browse_record;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtBrowseRecordBinding;

public class BrowseRecordFgt extends BaseFragment<FgtBrowseRecordBinding, BrowseRecordModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle(R.string.str_recently_browse);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_browse_record;
    }

    @Override
    public int getVariableId() {
        return BR.browseRecordModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
