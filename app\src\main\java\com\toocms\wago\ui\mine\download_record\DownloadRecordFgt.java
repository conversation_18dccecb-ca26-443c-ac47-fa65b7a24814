package com.toocms.wago.ui.mine.download_record;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtBrowseRecordBinding;
import com.toocms.wago.databinding.FgtDownloadRecordBinding;
import com.toocms.wago.ui.mine.browse_record.BrowseRecordModel;

public class DownloadRecordFgt extends BaseFragment<FgtDownloadRecordBinding, DownloadRecordModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle(R.string.str_download_record);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_download_record;
    }

    @Override
    public int getVariableId() {
        return BR.downloadRecordModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
