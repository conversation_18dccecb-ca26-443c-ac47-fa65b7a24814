package com.toocms.wago.ui.mine.download_record;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.CBSDBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;
import com.toocms.wago.ui.mine.browse_record.BrowseRecordModel;

public class DownloadRecordItemModel extends ItemViewModel<DownloadRecordModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public String id;
    public String type;

    public DownloadRecordItemModel(@NonNull DownloadRecordModel viewModel, CBSDBean.RowsBean rowsBean) {
        super(viewModel);
        id = rowsBean.id;
        type = rowsBean.type;
        url.set(rowsBean.thumUrl);
        title.set(rowsBean.title);
        subTitle.set(rowsBean.subTitle);
    }

    public BindingCommand startDetail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("productId", id);
        bundle.putString("productName", title.get());
        switch (type) {
            case "1":
                bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
                break;
            case "2":
                bundle.putString("detailType", Constants.DETAIL_TYPE_CERTIFICATE_FILE);
                break;
            case "3":
                bundle.putString("detailType", Constants.DETAIL_TYPE_MATERIAL_FILE);
                break;
            case "4":
                bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT_PLANS);
                break;
        }
        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
