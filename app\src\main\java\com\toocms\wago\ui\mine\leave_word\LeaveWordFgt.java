package com.toocms.wago.ui.mine.leave_word;

import com.blankj.utilcode.util.ColorUtils;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtLeaveWordBinding;

public class LeaveWordFgt extends BaseFragment<FgtLeaveWordBinding,LeaveWordModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setBackgroundColor(ColorUtils.getColor(R.color.clr_bg));
        topBar.setTitle(R.string.str_leave_word);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_leave_word;
    }

    @Override
    public int getVariableId() {
        return BR.leaveWordModel;
    }

    @Override
    protected void viewObserver() {

    }
}
