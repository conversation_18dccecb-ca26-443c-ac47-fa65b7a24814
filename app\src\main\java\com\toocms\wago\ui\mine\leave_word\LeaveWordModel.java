package com.toocms.wago.ui.mine.leave_word;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;

public class LeaveWordModel extends BaseViewModel<BaseModel> {

    public ObservableField<String> name = new ObservableField<>();
    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> content = new ObservableField<>();

    public LeaveWordModel(@NonNull Application application) {
        super(application);
    }

    public BindingCommand submit = new BindingCommand(() -> {
        addLiuYan();
    });

    public void addLiuYan() {
        ApiTool.postJson("leaveWord/addLiuYan")
                .add("context", content.get())
                .add("phone", phone.get())
                .add("realname", name.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    finishFragment();
                });
    }
}
