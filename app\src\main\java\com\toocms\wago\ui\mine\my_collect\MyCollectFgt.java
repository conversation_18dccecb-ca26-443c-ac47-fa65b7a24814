package com.toocms.wago.ui.mine.my_collect;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtMyCollectBinding;

public class MyCollectFgt extends BaseFragment<FgtMyCollectBinding, MyCollectModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle(R.string.str_my_collect);
        topBar.addRightImageButton(R.drawable.icon_setting, R.id.tag_setting).setOnClickListener(v -> {
            viewModel.changeEditStatus();
        });
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_my_collect;
    }

    @Override
    public int getVariableId() {
        return BR.myCollectModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
