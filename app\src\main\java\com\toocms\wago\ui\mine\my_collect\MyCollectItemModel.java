package com.toocms.wago.ui.mine.my_collect;


import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.bean.CBSDBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.details.DetailsFgt;
import com.toocms.wago.ui.mine.my_share.MyShareModel;

public class MyCollectItemModel extends ItemViewModel<MyCollectModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public ObservableField<Boolean> isEdit = new ObservableField<>();
    public String id;
    public String type;

    public MyCollectItemModel(@NonNull MyCollectModel viewModel, boolean isEdit, CBSDBean.RowsBean rowsBean) {
        super(viewModel);
        id = rowsBean.id;
        type = rowsBean.type;
        url.set(rowsBean.thumUrl);
        title.set(rowsBean.title);
        subTitle.set(rowsBean.subTitle);
        MyCollectItemModel.this.isEdit.set(isEdit);
        registerEditStatusMessenger();
    }

    public void registerEditStatusMessenger() {
        Messenger.getDefault().register(this, Constants.MESSENGER_TOKEN_EDIT_STATUS, Boolean.class, isEdit -> {
            MyCollectItemModel.this.isEdit.set(isEdit);
        });
    }

    public BindingCommand cancelCollect = new BindingCommand(() -> {
        addCollect();
    });

    public BindingCommand startDetail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("productId", id);
        bundle.putString("productName", title.get());
        switch (type) {
            case "1":
                bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
                break;
            case "2":
                bundle.putString("detailType", Constants.DETAIL_TYPE_CERTIFICATE_FILE);
                break;
            case "3":
                bundle.putString("detailType", Constants.DETAIL_TYPE_MATERIAL_FILE);
                break;
            case "4":
                bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT_PLANS);
                break;
        }
        viewModel.startFragment(DetailsFgt.class, bundle);
    });

    private void addCollect() {
        ApiTool.get("collect/addCollect")
//                .addHeader("Authorization", UserRepository.getInstance().getUser().token)
                .add("productId", id)
                .add("userid", UserRepository.getInstance().getUser().id)
                .add("type", type)
                .asTooCMSResponse(String.class)
                .withViewModel(viewModel)
                .request(s -> {
                    viewModel.showToast(s);
                    viewModel.items.remove(this);
                });
    }
}
