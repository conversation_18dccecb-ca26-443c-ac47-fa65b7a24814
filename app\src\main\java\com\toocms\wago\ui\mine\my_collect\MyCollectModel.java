package com.toocms.wago.ui.mine.my_collect;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CBSDBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.mine.my_share.MyShareItemModel;

public class MyCollectModel extends BaseViewModel<BaseModel> {

    public int p = 1;
    private boolean isEdit = false;

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<MyCollectItemModel> items = new ObservableArrayList<>();
    public ItemBinding<MyCollectItemModel> itemBinding = ItemBinding.of(BR.myCollectItemModel, R.layout.listitem_my_collect);

    public MyCollectModel(@NonNull Application application) {
        super(application);
        selectByCBSDlist(true);
    }

    public void changeEditStatus() {
        isEdit = !isEdit;
        Messenger.getDefault().send(isEdit, Constants.MESSENGER_TOKEN_EDIT_STATUS);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByCBSDlist(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByCBSDlist(false);
    });

    public void selectByCBSDlist(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("userid", UserRepository.getInstance().getUser().id);
        query.addProperty("type", 1);
        ApiTool.postJson("collect/selectByCBSDlist")
                .addHeader("Authorization", UserRepository.getInstance().getUser().token)
                .add("currentPage", p)
                .add("pageSize", 0)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(CBSDBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(cbsdBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(cbsdBean.rows, (index, item) -> {
                        items.add(new MyCollectItemModel(this, isEdit, item));
                    });
                });
    }
}
