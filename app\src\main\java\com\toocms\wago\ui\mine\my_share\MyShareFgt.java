package com.toocms.wago.ui.mine.my_share;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtMyShareBinding;

public class MyShareFgt extends BaseFragment<FgtMyShareBinding, MyShareModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle(R.string.str_my_share);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_my_share;
    }

    @Override
    public int getVariableId() {
        return BR.myShareModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
