package com.toocms.wago.ui.mine.my_share;

import android.app.Application;
import android.content.ClipData;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CBSDBean;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.module.ModuleClassifyItemModel;
import com.toocms.wago.ui.module.ModuleContentItemModel;

import java.io.File;

public class MyShareModel extends BaseViewModel<BaseModel> {

    public int p = 1;

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<MyShareItemModel> items = new ObservableArrayList<>();
    public ItemBinding<MyShareItemModel> itemBinding = ItemBinding.of(BR.myShareItemModel, R.layout.listitem_my_share);

    public MyShareModel(@NonNull Application application) {
        super(application);
        selectByCBSDlist(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByCBSDlist(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByCBSDlist(false);
    });

    public void selectByCBSDlist(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("userid", UserRepository.getInstance().getUser().id);
        query.addProperty("type", 3);
        ApiTool.postJson("collect/selectByCBSDlist")
                .add("currentPage", p)
                .add("pageSize", 0)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(CBSDBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(cbsdBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(cbsdBean.rows, (index, item) -> {
                        items.add(new MyShareItemModel(this, item));
                    });
                });
    }
}
