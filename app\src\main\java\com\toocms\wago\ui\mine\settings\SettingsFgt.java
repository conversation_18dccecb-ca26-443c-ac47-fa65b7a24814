package com.toocms.wago.ui.mine.settings;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtSettingsBinding;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class SettingsFgt extends BaseFragment<FgtSettingsBinding, SettingsViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("设置");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_settings;
    }

    @Override
    public int getVariableId() {
        return BR.settingsViewModel;
    }

    @Override
    protected void viewObserver() {

    }
}
