package com.toocms.wago.ui.mine.settings;

import android.app.Application;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.ActivityUtils;
import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.EncryptUtils;
import com.blankj.utilcode.util.FileUtils;
import com.blankj.utilcode.util.GsonUtils;
import com.qmuiteam.qmui.util.QMUIResHelper;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.configs.FileManager;
import com.toocms.tab.widget.update.TooCMSUpdateEntity;
import com.toocms.tab.widget.update.UpdateManager;
import com.toocms.tab.widget.update.XUpdate;
import com.toocms.tab.widget.update.entity.CheckVersionResult;
import com.toocms.tab.widget.update.entity.UpdateEntity;
import com.toocms.tab.widget.update.listener.IUpdateParseCallback;
import com.toocms.tab.widget.update.proxy.IUpdateParser;
import com.toocms.tab.widget.update.utils.UpdateUtils;
import com.toocms.wago.BuildConfig;
import com.toocms.wago.R;
import com.toocms.wago.ui.mine.settings.change_phone.ChangePhoneFgt;
import com.toocms.wago.ui.mine.settings.modify_nickname.ModifyNicknameFgt;
import com.toocms.wago.ui.mine.settings.modify_password.ModifyPasswordFgt;
import com.toocms.wago.ui.web.WebFgt;

import org.jetbrains.annotations.NotNull;

import static com.umeng.socialize.utils.ContextUtil.getPackageName;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class SettingsViewModel extends BaseViewModel {

    public ObservableField<String> version = new ObservableField<>();
    public ObservableField<String> size = new ObservableField<>();

    public SettingsViewModel(@NonNull @NotNull Application application) {
        super(application);
        version.set("WAGO " + AppUtils.getAppVersionName());
        size.set(FileUtils.getSize(FileManager.getCachePath()));
    }

    public BindingCommand update = new BindingCommand(() -> {
        XUpdate.newBuild(ActivityUtils.getTopActivity())
                .updateUrl(TooCMSApplication.getInstance().getAppConfig().getUpdateUrl())
                .param("apackage", EncryptUtils.encryptMD5ToString(getPackageName()))
                .updateParser(new TooCMSUpdateParser())
                .supportBackgroundUpdate(false)
                .promptThemeColor(QMUIResHelper.getAttrColor(ActivityUtils.getTopActivity(), R.attr.app_primary_color))
                .promptTopResId(R.drawable.xupdate_icon_app_rocket)
                .update();
    });

    /**
     * 自定义json解析器
     */
    static class TooCMSUpdateParser implements IUpdateParser {

        @Override
        public UpdateEntity parseJson(String json) {
            TooCMSUpdateEntity result = GsonUtils.fromJson(json, TooCMSUpdateEntity.class);
            if (result != null) {
                UpdateEntity entity = new UpdateEntity();
                entity
                        .setHasUpdate(result.getUpdate_status() != CheckVersionResult.NO_NEW_VERSION)
                        .setForce(result.getUpdate_status() == CheckVersionResult.HAVE_NEW_VERSION_FORCED_UPLOAD)
                        .setVersionCode(result.getVersion_code())
                        .setVersionName(result.getVersion_name())
                        .setUpdateContent(result.getDescription().replaceAll("\\\\r\\\\n", "\r\n"))
                        .setDownloadUrl(result.getUrl())
                        .setMd5(result.getApk_md5())
                        .setSize(result.getApk_size());
                return entity;
            }
            return null;
        }

        @Override
        public void parseJson(String json, IUpdateParseCallback callback) {
        }

        @Override
        public boolean isAsyncParser() {
            return false;
        }
    }

    public BindingCommand onModifyNicknameBindingCommand = new BindingCommand(() -> {
        startFragment(ModifyNicknameFgt.class);
    });

    public BindingCommand onChangePhoneBindingCommand = new BindingCommand(() -> {
        startFragment(ChangePhoneFgt.class);
    });

    public BindingCommand agreementBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("url", "https://www.app.hemajia.net/yinsitiaokuandl.html");
        startFragment(WebFgt.class, bundle);
    });

    public BindingCommand policyBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("url", "https://www.app.hemajia.net/yonghuxieyi.html");
        startFragment(WebFgt.class, bundle);
    });

    public BindingCommand onModifyPasswordBindingCommand = new BindingCommand(() -> {
        startFragment(ModifyPasswordFgt.class);
    });

    public BindingCommand onClearCacheBindingCommand = new BindingCommand(() -> {
        FileManager.clearCacheFiles();
        size.set(FileUtils.getSize(FileManager.getCachePath()));
        showToast("清理完毕");
    });
}
