package com.toocms.wago.ui.mine.settings.change_phone;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtChangePhoneBinding;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ChangePhoneFgt extends BaseFragment<FgtChangePhoneBinding, ChangePhoneViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("更改手机号");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_change_phone;
    }

    @Override
    public int getVariableId() {
        return BR.changePhoneViewModel;
    }

    @Override
    protected ChangePhoneViewModel getViewModel() {
        return new ChangePhoneViewModel(TooCMSApplication.getInstance(), getArguments() != null ? getArguments().getString("pageType") : null, getArguments() != null ? getArguments().getString("token") : null);
    }

    @Override
    protected void viewObserver() {

    }
}
