package com.toocms.wago.ui.mine.settings.change_phone;

import android.app.Application;
import android.os.Bundle;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.tab.network.RxHttp;
import com.toocms.wago.config.UserRepository;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ChangePhoneViewModel extends BaseViewModel {

    public static final String PAGE_TYPE_OLD = "PAGE_TYPE_OLD";
    public static final String PAGE_TYPE_NEW = "PAGE_TYPE_NEW";

    public ObservableBoolean isPhone = new ObservableBoolean(true);
    public ObservableField<String> tips = new ObservableField<>();
    public ObservableField<String> hint1 = new ObservableField<>();
    public ObservableField<String> hint2 = new ObservableField<>();
    public ObservableField<String> submit = new ObservableField<>();
    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> code = new ObservableField<>();
    public ObservableField<String> countdown = new ObservableField<>("获取验证码");
    public ObservableBoolean clickable = new ObservableBoolean(true);
    private long totalTime = 60;
    public String pageType;
    public String token;

    public ChangePhoneViewModel(@NonNull @NotNull Application application, String pageType, String token) {
        super(application);
        if (StringUtils.isEmpty(pageType)) {
            this.pageType = PAGE_TYPE_OLD;
        } else {
            this.pageType = pageType;
        }
        if (!StringUtils.isEmpty(token)) this.token = token;
        switch (this.pageType) {
            case PAGE_TYPE_OLD:
                tips.set("输入当前手机号并获取验证");
                hint1.set("请输入手机号");
                hint2.set("请输入验证码");
                submit.set("提交验证");
                break;
            case PAGE_TYPE_NEW:
                tips.set("输入新的手机号并获取验证");
                hint1.set("请输入手机号");
                hint2.set("请输入验证码");
                submit.set("提交修改");
                break;
        }
    }

    public BindingCommand onChangeBindingCommand = new BindingCommand(() -> {
        isPhone.set(!isPhone.get());
        tips.set("输入账号名称及密码验证身份");
        hint1.set("请输入账号名称");
        hint2.set("请输入账号密码");
    });

    public BindingCommand onCodeBindingCommand = new BindingCommand(this::call);

    public BindingCommand onSubmitBindingCommand = new BindingCommand(() -> {
        switch (this.pageType) {
            case PAGE_TYPE_OLD:
                updatePhone4Old();
                break;
            case PAGE_TYPE_NEW:
                updatePhone();
                break;
        }
    });

    private void updatePhone4Old() {
        RxHttp.postJson(TooCMSApplication.getInstance().getAppConfig().getBaseUrl() + (isPhone.get() ? "user/user/updatePhone4OldPhone" : "user/user/updatePhone4Username"))
                .add(isPhone.get() ? "phone" : "username", phone.get())
                .add(isPhone.get() ? "code" : "password", code.get())
                .add("id", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .subscribe(s -> {
                    Bundle bundle = new Bundle();
                    bundle.putString("pageType", PAGE_TYPE_NEW);
                    bundle.putString("token", s);
                    startFragment(ChangePhoneFgt.class, bundle, true);
                });
    }

    private void updatePhone() {
        ApiTool.postJson("user/user/updatePhone")
                .add("phone", phone.get())
                .add("code", code.get())
                .add("token", token)
                .add("id", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    finishFragment();
                });
    }

    private void sendUpdateNewPhoneVerificationCode() {
        ApiTool.get("user/user/sendUpdateNewPhoneVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(this::showToast);
    }

    private void sendUpdatePhoneVerificationCode() {
        ApiTool.get("user/user/sendUpdatePhoneVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(this::showToast);
    }

    private void call() {
        if (StringUtils.isEmpty(phone.get())) {
            showToast("请输入手机号");
            return;
        }
        clickable.set(false);
        timer.start();
        switch (pageType) {
            case PAGE_TYPE_OLD:
                sendUpdatePhoneVerificationCode();
                break;
            case PAGE_TYPE_NEW:
                sendUpdateNewPhoneVerificationCode();
                break;
        }
    }

    public CountDownTimer timer = new CountDownTimer(totalTime * 1000, 1000) {

        @Override
        public void onTick(long l) {
            countdown.set("重新获取(" + l / 1000 + "s)");
        }

        @Override
        public void onFinish() {
            countdown.set("获取验证码");
            clickable.set(true);
        }
    };
}
