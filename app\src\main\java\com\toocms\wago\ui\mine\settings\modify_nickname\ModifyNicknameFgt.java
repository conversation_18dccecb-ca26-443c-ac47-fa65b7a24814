package com.toocms.wago.ui.mine.settings.modify_nickname;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtModifyNicknameBinding;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ModifyNicknameFgt extends BaseFragment<FgtModifyNicknameBinding, ModifyNicknameViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("修改昵称");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_modify_nickname;
    }

    @Override
    public int getVariableId() {
        return BR.modifyNicknameViewModel;
    }

    @Override
    protected void viewObserver() {

    }
}
