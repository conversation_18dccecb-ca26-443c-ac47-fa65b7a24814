package com.toocms.wago.ui.mine.settings.modify_nickname;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.bean.User;
import com.toocms.wago.config.UserRepository;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ModifyNicknameViewModel extends BaseViewModel {

    public ObservableField<String> nickname = new ObservableField<>();

    public ModifyNicknameViewModel(@NonNull @NotNull Application application) {
        super(application);
        nickname.set(UserRepository.getInstance().getUser().nickname);
    }

    public BindingCommand onBindingCommand = new BindingCommand(() -> {
        updateNickname();
    });

    private void updateNickname() {
        ApiTool.postJson("user/user/updateNickname")
                .add("nickname", nickname.get())
                .add("id", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    UserRepository.getInstance().setUserInfo("nickname", nickname.get());
                    finishFragment();
                });
    }
}
