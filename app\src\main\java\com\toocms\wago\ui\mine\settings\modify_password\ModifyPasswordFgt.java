package com.toocms.wago.ui.mine.settings.modify_password;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtModifyPasswordBinding;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ModifyPasswordFgt extends BaseFragment<FgtModifyPasswordBinding, ModifyPasswordViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("修改密码");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_modify_password;
    }

    @Override
    public int getVariableId() {
        return BR.modifyPasswordViewModel;
    }

    @Override
    protected void viewObserver() {

    }
}
