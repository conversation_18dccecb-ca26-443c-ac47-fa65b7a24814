package com.toocms.wago.ui.mine.settings.modify_password;

import android.app.Application;
import android.os.CountDownTimer;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.StringUtils;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.config.UserRepository;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class ModifyPasswordViewModel extends BaseViewModel {

    public ObservableField<String> phone = new ObservableField<>();
    public ObservableField<String> code = new ObservableField<>();
    public ObservableField<String> password = new ObservableField<>();
    public ObservableField<String> countdown = new ObservableField<>("获取验证码");
    public ObservableBoolean clickable = new ObservableBoolean(true);
    private long totalTime = 60;

    public ModifyPasswordViewModel(@NonNull @NotNull Application application) {
        super(application);
    }

    public BindingCommand onCodeBindingCommand = new BindingCommand(this::call);

    public BindingCommand onSubmitBindingCommand = new BindingCommand(() -> {
        updatePassword();
    });

    private void updatePassword() {
        ApiTool.postJson("user/user/updatePassword")
                .add("phone", phone.get())
                .add("code", code.get())
                .add("password", password.get())
                .add("username", UserRepository.getInstance().getUser().id)
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(s -> {
                    showToast(s);
                    finishFragment();
                });
    }

    private void getVerify() {
        ApiTool.get("user/user/sendUpdatePasswordVerificationCode")
                .add("phone", phone.get())
                .asTooCMSResponse(String.class)
                .withViewModel(this)
                .request(this::showToast);
    }

    private void call() {
        if (StringUtils.isEmpty(phone.get())) {
            showToast("请输入手机号");
            return;
        }
        clickable.set(false);
        timer.start();
        getVerify();
    }

    public CountDownTimer timer = new CountDownTimer(totalTime * 1000, 1000) {

        @Override
        public void onTick(long l) {
            countdown.set("重新获取(" + l / 1000 + "s)");
        }

        @Override
        public void onFinish() {
            countdown.set("获取验证码");
            clickable.set(true);
        }
    };
}
