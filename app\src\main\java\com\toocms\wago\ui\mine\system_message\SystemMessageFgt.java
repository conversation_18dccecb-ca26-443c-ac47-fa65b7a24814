package com.toocms.wago.ui.mine.system_message;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtSystemMessageBinding;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class SystemMessageFgt extends BaseFragment<FgtSystemMessageBinding, SystemMessageViewModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setTitle("系统消息");
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_system_message;
    }

    @Override
    public int getVariableId() {
        return BR.systemMessageViewModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
