package com.toocms.wago.ui.mine.system_message;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.ItemViewModel;
import com.toocms.wago.bean.MessageBean;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class SystemMessageItemViewModel extends ItemViewModel {

    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> date = new ObservableField<>();
    public ObservableField<String> content = new ObservableField<>();

    public SystemMessageItemViewModel(@NonNull @NotNull BaseViewModel viewModel, MessageBean.RowsBean rowsBean) {
        super(viewModel);
        title.set(rowsBean.title);
        date.set(rowsBean.taskTime);
        content.set(rowsBean.zhengwenContext);
    }
}
