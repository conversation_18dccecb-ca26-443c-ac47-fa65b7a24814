package com.toocms.wago.ui.mine.system_message;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CBSDBean;
import com.toocms.wago.bean.MessageBean;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.mine.download_record.DownloadRecordItemModel;

import org.jetbrains.annotations.NotNull;

/**
 * Author：Zero
 * Date：2021/6/7
 */
public class SystemMessageViewModel extends BaseViewModel {

    public int p = 1;

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<SystemMessageItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<SystemMessageItemViewModel> itemBinding = ItemBinding.of(BR.systemMessageItemViewModel, R.layout.listitem_system_message);

    public SystemMessageViewModel(@NonNull @NotNull Application application) {
        super(application);
        selectMessage(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectMessage(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectMessage(false);
    });

    public void selectMessage(boolean isShowLoading) {
        ApiTool.postJson("message/selectMessage")
                .add("currentPage", p)
                .add("pageSize", 0)
                .add("query", "")
                .asTooCMSResponse(MessageBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(messageBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(messageBean.rows, (index, item) -> {
                        items.add(new SystemMessageItemViewModel(this, item));
                    });
                });
    }
}
