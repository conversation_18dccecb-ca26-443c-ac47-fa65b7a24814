package com.toocms.wago.ui.module;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.LogUtils;
import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.wago.bean.ByerjiYeMianBean;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.config.Constants;

import java.util.List;

public class ModuleClassifyItemModel extends ItemViewModel<ModuleModel> {

    public CategoryAndProductBean.ListBean listBean;
    public ObservableField<Boolean> isSelected = new ObservableField<>();
    public ObservableField<String> classify = new ObservableField<>();
    public String categoryId;

    public BindingCommand onClickBindingCommand = new BindingCommand(() -> {
        Messenger.getDefault().send(this, Constants.MESSENGER_TOKEN_TOP_CLASSIFY_CLICK);
        if (viewModel.isProduct.get()) {
            viewModel.p = 1;
            viewModel.categoryId2 = categoryId;
            viewModel.additionalFunctionId = "";
            viewModel.typeId = "";
            viewModel.functionId = "";
            viewModel.listBean = listBean;
            viewModel.selectByCategoryId2(true);
//            viewModel.selectByPageAndCondition(true);
//            viewModel.isShowScreen.setValue(listBean);
        } else {
            viewModel.selectByerjiYeMian2(categoryId, true);
        }
    });

    public ModuleClassifyItemModel(@NonNull ModuleModel viewModel, CategoryAndProductBean.ListBean listBean) {
        super(viewModel);
        this.listBean = listBean;
        categoryId = listBean.categoryId2;
        classify.set(listBean.categoryName);
        registerTopClassifyClickMessenger();
    }

    public ModuleClassifyItemModel(@NonNull ModuleModel viewModel, ByerjiYeMianBean.ProductPlanCategoriesBean productPlanCategoriesBean) {
        super(viewModel);
        categoryId = productPlanCategoriesBean.productPlanCategoryId;
        classify.set(productPlanCategoriesBean.name);
        registerTopClassifyClickMessenger();
    }

    private void registerTopClassifyClickMessenger() {
        Messenger.getDefault().register(this, Constants.MESSENGER_TOKEN_TOP_CLASSIFY_CLICK, ModuleClassifyItemModel.class, itemViewModel -> {
            isSelected.set(this == itemViewModel);
            viewModel.categoryId2 = categoryId;
        });
    }
}
