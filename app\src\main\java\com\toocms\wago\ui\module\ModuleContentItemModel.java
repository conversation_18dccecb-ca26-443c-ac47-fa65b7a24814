package com.toocms.wago.ui.module;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ByerjiYeMianBean;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.bean.ModuleContent;
import com.toocms.wago.bean.ProductBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;
import com.toocms.wago.ui.module.list.ModuleDetailFgt;

public class ModuleContentItemModel extends ItemViewModel<ModuleModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> type = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public String productId;
    public String productId2;
    public boolean isProduct;

    public ModuleContentItemModel(@NonNull ModuleModel viewModel, ModuleContent productBean) {
        super(viewModel);
        isProduct = true;
        productId = productBean.id;
        productId2 = productBean.belongCategoryId2;
        url.set(productBean.url);
        title.set(productBean.name);
    }

    public ModuleContentItemModel(@NonNull ModuleModel viewModel, ByerjiYeMianBean.ProductPlansBean productPlansBean) {
        super(viewModel);
        isProduct = false;
        productId = productPlansBean.productPlanId;
        url.set(productPlansBean.thumbnailUrl);
        title.set(productPlansBean.totalTitle);
        type.set(productPlansBean.productType);
        subTitle.set(productPlansBean.subhead);
    }

    public BindingCommand detail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("productId", productId);
        bundle.putString("productId2", productId2);
        bundle.putString("product", title.get());
        bundle.putBoolean("isProduct", isProduct);
        if (isProduct)
            viewModel.startFragment(ModuleDetailFgt.class, bundle);
        else {
            bundle.putString("detailType", isProduct ? Constants.DETAIL_TYPE_PRODUCT : Constants.DETAIL_TYPE_PRODUCT_PLANS);
            bundle.putString("productName", title.get());
            viewModel.startFragment(DetailsFgt.class, bundle);
        }
//        Bundle bundle = new Bundle();
//        bundle.putString("detailType", isProduct ? Constants.DETAIL_TYPE_PRODUCT : Constants.DETAIL_TYPE_PRODUCT_PLANS);
//        bundle.putString("productId", productId);
//        bundle.putString("productId2", productId2);
//        bundle.putString("productName", title.get());
//        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
