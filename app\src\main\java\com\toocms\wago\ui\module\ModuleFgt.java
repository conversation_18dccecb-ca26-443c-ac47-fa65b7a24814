package com.toocms.wago.ui.module;

import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.qmuiteam.qmui.widget.popup.QMUIPopup;
import com.qmuiteam.qmui.widget.popup.QMUIPopups;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtModuleBinding;
import com.toocms.wago.ui.module.adt.ModuleFilterAdt;

public class ModuleFgt extends BaseFragment<FgtModuleBinding, ModuleModel> {

    QMUIPopup typesPop;
    QMUIPopup functionsPop;
    QMUIPopup appendFunctionsPop;

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
//        binding.refresh.setEnableLoadMore(getArguments().getBoolean("isProduct"));
        binding.refresh.setEnableRefresh(getArguments().getBoolean("isProduct"));
        binding.filterCl.addOnLayoutChangeListener((v, left, top, right, bottom, oldLeft, oldTop, oldRight, oldBottom) -> {
            binding.classifyRv.setPadding(0, bottom - top, 0, 0);
        });
        binding.typeTv.setOnClickListener(v -> typesPop.show(v));
        binding.functionTv.setOnClickListener(v -> functionsPop.show(v));
        binding.appendFunctionTv.setOnClickListener(v -> appendFunctionsPop.show(v));
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_module;
    }

    @Override
    public int getVariableId() {
        return BR.moduleModel;
    }

    @Override
    protected ModuleModel getViewModel() {
        return new ModuleModel(TooCMSApplication.getInstance(), getArguments().getString("categoryId"), getArguments().getString("product"), getArguments().getBoolean("isProduct"));
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
        viewModel.headView.observe(this, headView -> {
            ViewParent parent = headView.getParent();
            if (null != parent) {
                ((ViewGroup) parent).removeView(headView);
            }
            binding.headFl.removeAllViews();
            binding.headFl.addView(headView);
        });
//        viewModel.isShowScreen.observe(this, listBean -> {
//            if (null == listBean || CollectionUtils.isEmpty(listBean.list))
//                binding.filterCl.setVisibility(View.GONE);
//            else {
//                binding.filterCl.setVisibility(View.VISIBLE);
//                if (!listBean.list.contains("产品类型"))
//                    binding.typeTv.setVisibility(View.GONE);
//                else {
//                    binding.typeTv.setVisibility(View.VISIBLE);
//                    viewModel.selectByProductType(listBean.categoryId2, 1);
//                }
//                if (!listBean.list.contains("产品功能"))
//                    binding.functionTv.setVisibility(View.GONE);
//                else {
//                    binding.functionTv.setVisibility(View.VISIBLE);
//                    viewModel.selectByProductType(listBean.categoryId2, 2);
//                }
//                if (!listBean.list.contains("附加功能"))
//                    binding.appendFunctionTv.setVisibility(View.GONE);
//                else {
//                    binding.appendFunctionTv.setVisibility(View.VISIBLE);
//                    viewModel.selectByProductType(listBean.categoryId2, 3);
//                }
//            }
//        });
//        viewModel.typesPopEvent.observe(this, v -> typesPop = QMUIPopups.listPopup(getContext()
//                , ConvertUtils.dp2px(120)
//                , ConvertUtils.dp2px(500)
//                , new ModuleFilterAdt(viewModel.types)
//                , (parent, view1, position, id) -> {
//                    view1.setSelected(!view1.isSelected());
//                    viewModel.typeId = viewModel.types.get(position).typeId;
//                    viewModel.functionId = "";
//                    viewModel.additionalFunctionId = "";
//                    viewModel.selectByPageAndCondition(true);
//                    typesPop.dismiss();
//                }));
//        viewModel.functionsPopEvent.observe(this, v -> functionsPop = QMUIPopups.listPopup(getContext()
//                , ConvertUtils.dp2px(120)
//                , ConvertUtils.dp2px(500)
//                , new ModuleFilterAdt(viewModel.functions)
//                , (parent, view1, position, id) -> {
//                    view1.setSelected(!view1.isSelected());
//                    viewModel.typeId = "";
//                    viewModel.functionId = viewModel.functions.get(position).typeId;
//                    viewModel.additionalFunctionId = "";
//                    viewModel.selectByPageAndCondition(true);
//                    functionsPop.dismiss();
//                }));
//        viewModel.appendFunctionsPopEvent.observe(this, v -> appendFunctionsPop = QMUIPopups.listPopup(getContext()
//                , ConvertUtils.dp2px(120)
//                , ConvertUtils.dp2px(500)
//                , new ModuleFilterAdt(viewModel.appendFunctions)
//                , (parent, view1, position, id) -> {
//                    view1.setSelected(!view1.isSelected());
//                    viewModel.typeId = "";
//                    viewModel.functionId = "";
//                    viewModel.additionalFunctionId = viewModel.appendFunctions.get(position).typeId;
//                    viewModel.selectByPageAndCondition(true);
//                    appendFunctionsPop.dismiss();
//                }));
    }
}
