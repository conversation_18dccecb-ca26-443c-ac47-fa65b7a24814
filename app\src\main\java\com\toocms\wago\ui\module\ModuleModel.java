package com.toocms.wago.ui.module;

import android.app.Application;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.webkit.WebView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.google.gson.JsonObject;
import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.imageload.ImageLoader;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.ByerjiYeMianBean;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.bean.ModuleContent;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.bean.ProductListBean;
import com.toocms.wago.bean.ProductManualBean;
import com.toocms.wago.bean.ProductTypeBean;
import com.toocms.wago.ui.download_material.DownloadMaterialFileItemModel;
import com.toocms.wago.ui.index.IndexProductItemModel;
import com.toocms.wago.ui.product_libs.ProductLibsTopClassifyModel;

import java.util.List;

public class ModuleModel extends BaseViewModel<BaseModel> {

    public int p = 1;
    public String categoryId;
    public String categoryId2;
    public String additionalFunctionId;
    public String functionId;
    public String typeId;
    public List<ProductTypeBean> types;
    public List<ProductTypeBean> functions;
    public List<ProductTypeBean> appendFunctions;
    public SingleLiveEvent<CategoryAndProductBean.ListBean> isShowScreen = new SingleLiveEvent<>();
    public CategoryAndProductBean.ListBean listBean;
    public ObservableField<String> categoryName = new ObservableField<>();
    public ObservableField<String> query = new ObservableField<>();
    public ObservableBoolean isProduct = new ObservableBoolean();

    public SingleLiveEvent<View> headView = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> typesPopEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> functionsPopEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> appendFunctionsPopEvent = new SingleLiveEvent<>();

    public ObservableArrayList<ModuleClassifyItemModel> classifyItems = new ObservableArrayList<>();
    public ItemBinding<ModuleClassifyItemModel> classifyItemBinding = ItemBinding.of(BR.moduleClassifyItemModel, R.layout.listitem_module_classify);
    public ObservableArrayList<ModuleContentItemModel> contentItems = new ObservableArrayList<>();
    public ItemBinding<ModuleContentItemModel> contentItemBinding = ItemBinding.of(BR.moduleContentItemModel, R.layout.listitem_module);

    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public ModuleModel(@NonNull Application application, String categoryId, String categoryName, boolean isProduct) {
        super(application);
        this.categoryId = categoryId;
        this.isProduct.set(isProduct);
        this.categoryName.set(categoryName);
        if (isProduct)
            selectProductManualAPP(true);
        else {
            isShowScreen.setValue(null);
            selectByerjiYeMian(true);
        }
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByCategoryId2(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByCategoryId2(false);
    });

    public BindingCommand<Integer> onKey = new BindingCommand<>(keyCode -> {
        if (keyCode == KeyEvent.KEYCODE_ENTER) {
            p = 1;
            selectByCategoryId2(true);
        }
    });

    private View videoHead() {
        ImageView result = new ImageView(getApplication());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ConvertUtils.dp2px(140));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.topMargin = ConvertUtils.dp2px(10);
        layoutParams.leftMargin = ConvertUtils.dp2px(15);
        layoutParams.rightMargin = ConvertUtils.dp2px(15);
        layoutParams.bottomMargin = ConvertUtils.dp2px(15);
        result.setLayoutParams(layoutParams);
        result.setImageResource(R.mipmap.img_default);
        result.setScaleType(ImageView.ScaleType.CENTER_CROP);
        return result;
    }

    private View introHead(String url, String text) {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_intro, null);
        ImageView coverIv = result.findViewById(R.id.cover_iv);
        TextView introTv = result.findViewById(R.id.intro_tv);
        ImageLoader.loadUrl2Image(url, coverIv, R.mipmap.img_default);
        introTv.setText(text);
        return result;
    }

    private View webViewHead(String html) {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_webview, null);
        QMUIWebView qmuiWebView = result.findViewById(R.id.webview);
        qmuiWebView.loadDataWithBaseURL(null, html, "text/html", "UTF-8", null);
        return result;
    }

    private View muchIntroHead() {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_much_intro, null);
        ImageView coverIv = result.findViewById(R.id.cover_iv);
        LinearLayoutCompat introLl = result.findViewById(R.id.intro_ll);
        for (int i = 0; i < 5; i++) {
            View item = LayoutInflater.from(getApplication()).inflate(R.layout.listitem_module_head_much_intro, introLl, false);
            TextView contentTv = item.findViewById(R.id.content_tv);
            contentTv.setText("简介简介简介简介简介简介简介简介简介简介简介简介简介简介简介简介");
            introLl.addView(item);
        }
        return result;
    }

    public void selectProductManualAPP(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("categoryId", categoryId);
        query.addProperty("title", "");
        ApiTool.postJson("product/selectByCategoryId1")
                .add("currentPage", p)
                .add("pageSize", 0)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(CategoryAndProductBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(categoryAndProductBean -> {
                    headView.setValue(introHead(categoryAndProductBean.thumbnailUrl, categoryAndProductBean.contextOne));
                    CollectionUtils.forAllDo(categoryAndProductBean.list, (index, item) -> {
                        classifyItems.add(new ModuleClassifyItemModel(this, item));
                    });
                    if (CollectionUtils.isNotEmpty(classifyItems))
                        classifyItems.get(0).onClickBindingCommand.execute();
                });
    }

    public void selectByCategoryId2(boolean isShowLoading) {
        ApiTool.get("product/selectByCategoryId2")
                .add("categoryid", categoryId2)
                .asTooCMSResponseList(ModuleContent.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(moduleContents -> {
                    contentItems.clear();
                    onRefreshFinish.call();
                    CollectionUtils.forAllDo(moduleContents, (index, item) -> {
                        contentItems.add(new ModuleContentItemModel(this, item));
                    });
                });
    }

//    @Deprecated
//    public void selectByPageAndCondition(boolean isShowLoading) {
//        JsonObject query = new JsonObject();
//        query.addProperty("additionalFunctionId", additionalFunctionId);
//        query.addProperty("categoryId1", categoryId);
//        query.addProperty("categoryId2", categoryId2);
//        query.addProperty("functionId", functionId);
//        query.addProperty("typeId", typeId);
//        query.addProperty("totalTitle", this.query.get());
//        ApiTool.postJson("product/selectByPageAndCondition")
//                .add("currentPage", p)
//                .add("pageSize", 0)
//                .addJsonElement("query", query.toString())
//                .asTooCMSResponse(ProductListBean.class)
//                .withViewModel(this)
//                .showLoading(isShowLoading)
//                .request(productListBean -> {
//                    if (p == 1) {
//                        contentItems.clear();
//                        onRefreshFinish.call();
//                    } else onLoadMoreFinish.call();
//                    CollectionUtils.forAllDo(productListBean.rows, (index, item) -> {
//                        contentItems.add(new ModuleContentItemModel(this, item));
//                    });
//                });
//    }

    public void selectByProductType(String categoryId, int type) {
        ApiTool.get("product/selectByProductType")
                .add("categoryId", categoryId)
                .add("type", type)
                .asTooCMSResponseList(ProductTypeBean.class)
                .withViewModel(this)
                .showLoading(false)
                .request(productTypeBeans -> {
                    switch (type) {
                        case 1: // 类型
                            types = productTypeBeans;
                            typesPopEvent.call();
                            break;
                        case 2: // 功能
                            functions = productTypeBeans;
                            functionsPopEvent.call();
                            break;
                        case 3: // 附加
                            appendFunctions = productTypeBeans;
                            appendFunctionsPopEvent.call();
                            break;
                    }
                });
    }

    public void selectByerjiYeMian(boolean isShowLoading) {
        ApiTool.get("pPlan/selectByerjiYeMian")
                .add("categoryId", categoryId)
                .asTooCMSResponse(ByerjiYeMianBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(byerjiYeMianBean -> {
                    CollectionUtils.forAllDo(byerjiYeMianBean.productPlanCategories, (index, item) -> {
                        classifyItems.add(new ModuleClassifyItemModel(this, item));
                    });
                    if (CollectionUtils.isNotEmpty(classifyItems))
                        classifyItems.get(0).onClickBindingCommand.execute();
                });
    }

    public void selectByerjiYeMian2(String categoryId, boolean isShowLoading) {
        ApiTool.get("pPlan/selectByerjiYeMian")
                .add("categoryId", categoryId)
                .asTooCMSResponse(ByerjiYeMianBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(byerjiYeMianBean -> {
                    contentItems.clear();
                    CollectionUtils.forAllDo(byerjiYeMianBean.productPlans, (index, item) -> {
                        contentItems.add(new ModuleContentItemModel(this, item));
                    });
                });
    }
}
