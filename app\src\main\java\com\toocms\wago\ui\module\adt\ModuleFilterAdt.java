package com.toocms.wago.ui.module.adt;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

import com.google.gson.internal.$Gson$Preconditions;
import com.toocms.wago.R;
import com.toocms.wago.bean.ProductTypeBean;

import java.util.ArrayList;
import java.util.List;

public class ModuleFilterAdt extends BaseAdapter {

    public List<ProductTypeBean> filterList = new ArrayList<>();

    public ModuleFilterAdt(List<ProductTypeBean> filterList) {
        if (null == filterList) {
            filterList = new ArrayList<>();
        }
        this.filterList.addAll(filterList);
    }

    @Override
    public int getCount() {
        return filterList.size();
    }

    @Override
    public Object getItem(int position) {
        return null;
    }

    @Override
    public long getItemId(int position) {
        return 0;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder holder;
        if (null == convertView) {
            convertView = LayoutInflater.from(parent.getContext()).inflate(R.layout.listitem_module_filter, parent, false);
            holder = new ViewHolder(convertView);
            convertView.setTag(R.id.tag_item, holder);
        } else {
            holder = (ViewHolder) convertView.getTag(R.id.tag_item);
        }
        holder.contentTv.setText(filterList.get(position).typeName + "(" + filterList.get(position).typenum + ")");
        return holder.getItemView();
    }

    public static class ViewHolder {
        private View itemView;
        private TextView contentTv;

        public ViewHolder(View itemView) {
            this.itemView = itemView;
            this.contentTv = itemView.findViewById(R.id.content_tv);
        }

        public View getItemView() {
            return itemView;
        }
    }
}
