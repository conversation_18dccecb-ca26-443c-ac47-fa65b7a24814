package com.toocms.wago.ui.module.list;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ByerjiYeMianBean;
import com.toocms.wago.bean.ProductBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

public class ModuleDetailItemModel extends ItemViewModel<ModuleDetailModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> type = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public String productId;
    public boolean isProduct;

    public ModuleDetailItemModel(@NonNull ModuleDetailModel viewModel, ProductBean productBean) {
        super(viewModel);
        isProduct = true;
        productId = productBean.productId;
        url.set(productBean.thumbnailUrl);
        title.set(productBean.totalTitle);
        type.set(productBean.productType);
        subTitle.set(productBean.subhead);
    }

    public ModuleDetailItemModel(@NonNull ModuleDetailModel viewModel, ByerjiYeMianBean.ProductPlansBean productPlansBean) {
        super(viewModel);
        isProduct = false;
        productId = productPlansBean.productPlanId;
        url.set(productPlansBean.thumbnailUrl);
        title.set(productPlansBean.totalTitle);
        type.set(productPlansBean.productType);
        subTitle.set(productPlansBean.subhead);
    }

    public BindingCommand detail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("detailType", isProduct ? Constants.DETAIL_TYPE_PRODUCT : Constants.DETAIL_TYPE_PRODUCT_PLANS);
        bundle.putString("productId", productId);
        bundle.putString("productName", title.get());
        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
