package com.toocms.wago.ui.module.list;

import android.app.Application;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.LinearLayoutCompat;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableBoolean;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.google.gson.JsonObject;
import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.imageload.ImageLoader;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.bean.ProductListBean;
import com.toocms.wago.bean.ProductTypeBean;
import com.toocms.wago.bean.SelectByCategory3;
import com.toocms.wago.config.UserRepository;

import java.util.List;

public class ModuleDetailModel extends BaseViewModel<BaseModel> {

    public int p = 1;
    public String categoryId;
    public String categoryId2;
    public String additionalFunctionId;
    public String functionId;
    public String seriesId;
    public String typeId;
    public List<ProductTypeBean> types;
    public List<ProductTypeBean> functions;
    public List<ProductTypeBean> appendFunctions;
    public List<ProductTypeBean> categorySeries;
    public SingleLiveEvent<List<String>> isShowScreen = new SingleLiveEvent<>();
    public ObservableField<String> categoryName = new ObservableField<>();
    public ObservableField<String> query = new ObservableField<>();
    public ObservableBoolean isProduct = new ObservableBoolean();

    public SingleLiveEvent<View> headView = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> typesPopEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> functionsPopEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> appendFunctionsPopEvent = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> categorySeriesPopEvent = new SingleLiveEvent<>();

    public ObservableArrayList<ModuleDetailItemModel> contentItems = new ObservableArrayList<>();
    public ItemBinding<ModuleDetailItemModel> contentItemBinding = ItemBinding.of(BR.moduleDetailItemModel, R.layout.listitem_module_content);

    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public ModuleDetailModel(@NonNull Application application, String categoryId, String categoryId2, String categoryName, boolean isProduct) {
        super(application);
        this.categoryId = categoryId;
        this.categoryId2 = categoryId2;
        this.isProduct.set(isProduct);
        this.categoryName.set(categoryName);
        selectByCategory3(true);
        selectByPageAndCondition(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByPageAndCondition(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByPageAndCondition(false);
    });

    public BindingCommand<Integer> onKey = new BindingCommand<>(keyCode -> {
        if (keyCode == KeyEvent.KEYCODE_ENTER) {
            p = 1;
            selectByPageAndCondition(true);
        }
    });

    private View videoHead() {
        ImageView result = new ImageView(getApplication());
        FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ConvertUtils.dp2px(140));
        layoutParams.gravity = Gravity.CENTER;
        layoutParams.topMargin = ConvertUtils.dp2px(10);
        layoutParams.leftMargin = ConvertUtils.dp2px(15);
        layoutParams.rightMargin = ConvertUtils.dp2px(15);
        layoutParams.bottomMargin = ConvertUtils.dp2px(15);
        result.setLayoutParams(layoutParams);
        result.setImageResource(R.mipmap.img_default);
        result.setScaleType(ImageView.ScaleType.CENTER_CROP);
        return result;
    }

    private View introHead(String url, String text) {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_intro, null);
        ImageView coverIv = result.findViewById(R.id.cover_iv);
        TextView introTv = result.findViewById(R.id.intro_tv);
        ImageLoader.loadUrl2Image(url, coverIv, R.mipmap.img_default);
        introTv.setText(text);
        return result;
    }

    private View webViewHead(String html) {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_webview, null);
        QMUIWebView qmuiWebView = result.findViewById(R.id.webview);
        qmuiWebView.loadDataWithBaseURL(null, html, "text/html", "UTF-8", null);
        return result;
    }

    private View muchIntroHead() {
        View result = LayoutInflater.from(getApplication()).inflate(R.layout.layout_module_head_much_intro, null);
        ImageView coverIv = result.findViewById(R.id.cover_iv);
        LinearLayoutCompat introLl = result.findViewById(R.id.intro_ll);
        for (int i = 0; i < 5; i++) {
            View item = LayoutInflater.from(getApplication()).inflate(R.layout.listitem_module_head_much_intro, introLl, false);
            TextView contentTv = item.findViewById(R.id.content_tv);
            contentTv.setText("简介简介简介简介简介简介简介简介简介简介简介简介简介简介简介简介");
            introLl.addView(item);
        }
        return result;
    }

    public void selectByCategory3(boolean isShowLoading) {
        ApiTool.get("product/selectByCategory3")
                .add("categoryid", categoryId)
                .asTooCMSResponse(SelectByCategory3.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(selectByCategory3 -> {
                    headView.setValue(introHead(selectByCategory3.thumbnailUrl, selectByCategory3.contextOne));
                    isShowScreen.setValue(selectByCategory3.list);
                });
    }

    public void selectByPageAndCondition(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("additionalFunctionId", additionalFunctionId);
//        query.addProperty("categoryId1", categoryId);
//        query.addProperty("categoryId2", categoryId2);
        query.addProperty("userid", UserRepository.getInstance().getUser().id);
        query.addProperty("categoryId3", categoryId);
        query.addProperty("functionId", functionId);
        query.addProperty("seriesId", seriesId);
        query.addProperty("typeId", typeId);
        query.addProperty("totalTitle", this.query.get());
        ApiTool.postJson("product/selectByPageAndCondition")
                .add("currentPage", p)
                .add("pageSize", 0)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(ProductListBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(productListBean -> {
                    if (p == 1) {
                        contentItems.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(productListBean.rows, (index, item) -> {
                        contentItems.add(new ModuleDetailItemModel(this, item));
                    });
                });
    }

    public void selectByProductType(String categoryId, int type) {
        ApiTool.get("product/selectByProductType")
                .add("categoryId", categoryId)
                .add("type", type)
                .asTooCMSResponseList(ProductTypeBean.class)
                .withViewModel(this)
                .showLoading(false)
                .request(productTypeBeans -> {
                    switch (type) {
                        case 1: // 类型
                            types = productTypeBeans;
                            typesPopEvent.call();
                            break;
                        case 2: // 功能
                            functions = productTypeBeans;
                            functionsPopEvent.call();
                            break;
                        case 3: // 附加
                            appendFunctions = productTypeBeans;
                            appendFunctionsPopEvent.call();
                            break;
                        case 4: // 产品系列
                            categorySeries = productTypeBeans;
                            categorySeriesPopEvent.call();
                            break;
                    }
                });
    }
}
