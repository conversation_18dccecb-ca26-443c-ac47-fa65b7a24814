package com.toocms.wago.ui.newest_product;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtNewestProductBinding;

public class NewestProductFgt extends BaseFragment<FgtNewestProductBinding,NewestProductModel> {
    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_newest_product;
    }

    @Override
    public int getVariableId() {
        return BR.newestProductModel;
    }

    @Override
    protected void viewObserver() {

    }
}
