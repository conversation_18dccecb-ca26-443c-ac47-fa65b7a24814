package com.toocms.wago.ui.newest_product;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.recyclerview.widget.GridLayoutManager;

import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.wago.BR;
import com.toocms.wago.R;

public class NewestProductModel extends BaseViewModel {
    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {

    });

    public GridLayoutManager.SpanSizeLookup spanSizeLookup=new GridLayoutManager.SpanSizeLookup() {
        @Override
        public int getSpanSize(int position) {
            int result=0;
            MultiItemViewModel item = items.get(position);
            return result;
        }
    };

    public NewestProductModel(@NonNull Application application) {
        super(application);
    }
}
