package com.toocms.wago.ui.photoview;


import android.view.View;
import android.view.ViewGroup;

import androidx.viewpager.widget.PagerAdapter;

import com.toocms.wago.widget.photoview.PhotoView;

import java.util.List;


/**
 * Created by Toocms on 2017/3/13.
 */
public class PhotoViewAdap extends PagerAdapter {

    public List<PhotoView> views;

    public PhotoViewAdap(List<PhotoView> views) {
        this.views = views;
    }

    /**
     * PagerAdapter管理数据大小
     */
    @Override
    public int getCount() {
        if (views != null) {
            return views.size();
        }
        return 0;
    }

    /**
     * 关联key 与 obj是否相等，即是否为同一个对象
     */
    @Override
    public boolean isViewFromObject(View view, Object obj) {
        return view == obj; // key
    }

    /**
     * 销毁当前page的相隔2个及2个以上的item时调用
     */
    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View) object); // 将view 类型 的object熊容器中移除,根据key
    }

    /**
     * 当前的page的前一页和后一页也会被调用，如果还没有调用或者已经调用了destroyItem
     */
    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        container.addView(views.get(position));
        return views.get(position); // 返回该view对象，作为key
    }
}
