package com.toocms.wago.ui.photoview;

import android.graphics.Bitmap;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager.widget.ViewPager;

import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.ImageUtils;
import com.blankj.utilcode.util.LogUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.request.RequestOptions;
import com.bumptech.glide.request.target.SimpleTarget;
import com.bumptech.glide.request.transition.Transition;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.config.Constants;
import com.toocms.wago.databinding.FgtPhotoviewBinding;
import com.toocms.wago.widget.photoview.PhotoView;
import com.toocms.wago.widget.photoview.PhotoViewAttacher;

import java.util.ArrayList;
import java.util.List;

public class PhotoViewFgt extends BaseFragment<FgtPhotoviewBinding, PhotoViewModel> {
    private List<String> images;
    private List<PhotoView> lists;
    private int initializePosition = 0;

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
        Bundle arguments = getArguments();
        images = arguments.getStringArrayList(Constants.KEY_IMAGES);
        initializePosition = arguments.getInt(Constants.KEY_POSITION, 0);
//        binding.saveTv.setOnClickListener(v -> {
//            String url = images.get(binding.photoviewVpPage.getCurrentItem());
//            Glide.with(getContext()).asBitmap().load(url).into(new SimpleTarget<Bitmap>() {
//                @Override
//                public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
//                    ImageUtils.save2Album(resource, Bitmap.CompressFormat.PNG);
//                    showToast(R.string.str_save_succeed_hint);
//                }
//            });
//        });
        lists = new ArrayList<>();

        PhotoView p;
        for (int i = 0; i < images.size(); i++) {
            p = new PhotoView(getContext());
            // Glide.with(getApplicationContext()).load(data.get(i)).placeholder(R.drawable.a2).error(R.drawable.a2).into(p);
            Glide.with(getContext().getApplicationContext()).setDefaultRequestOptions(new RequestOptions().error(R.mipmap.img_default)).load(images.get(i)).into(p);
            lists.add(p);
            p.setOnPhotoTapListener(new PhotoViewAttacher.OnPhotoTapListener() {

                @Override
                public void onPhotoTap(View view, float x, float y) {
                    finishFragment();
                }

                @Override
                public void onOutsidePhotoTap() {

                }
            });

            p.setOnLongClickListener(view -> {
                showItemsDialog("选项", new String[]{"保存图片"}, (dialogInterface, j) -> {
                    String url = images.get(binding.photoviewVpPage.getCurrentItem());
                    Glide.with(getContext()).asBitmap().load(url).into(new SimpleTarget<Bitmap>() {
                        @Override
                        public void onResourceReady(@NonNull Bitmap resource, @Nullable Transition<? super Bitmap> transition) {
                            ImageUtils.save2Album(resource, Bitmap.CompressFormat.PNG);
                            showToast("保存成功");
                        }
                    });
                    dialogInterface.dismiss();
                });
                return false;
            });
        }

        binding.photoviewVpPage.setOnPageChangeListener(new ViewPager.OnPageChangeListener() {
            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
            }

            @Override
            public void onPageSelected(int position) {
                updataNumber(images.size(), position);
                updataNumber(position);
            }

            @Override
            public void onPageScrollStateChanged(int state) {
            }
        });
        binding.photoviewVpPage.setOnClickListener(view -> finishFragment());
        binding.photoviewVpPage.setCurrentItem(initializePosition);
        binding.photoviewVpPage.setAdapter(new PhotoViewAdap(lists));
        initNumber(images.size());
        updataNumber(initializePosition);
        updataNumber(images.size(), initializePosition);
        binding.photoviewVpPage.setCurrentItem(initializePosition);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_photoview;
    }

    @Override
    public int getVariableId() {
        return BR.photoViewModel;
    }

    @Override
    protected void viewObserver() {

    }


    private void updataNumber(int size, int position) {
        binding.photoviewTvNumber.setText((position + 1) + "/" + size);
    }

    private void updataNumber(int position) {
        int count = binding.photoviewLinlayNumber.getChildCount();
        for (int i = 0; i < count; i++) {
            View child = binding.photoviewLinlayNumber.getChildAt(i);
            if (position == i) {
                child.setSelected(true);
            } else {
                child.setSelected(false);
            }
        }
    }

    private void initNumber(int size) {
        for (int i = 0; i < size; i++) {
            ViewGroup.MarginLayoutParams layoutParams = new ViewGroup.MarginLayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
            layoutParams.width = ConvertUtils.dp2px(5);
            layoutParams.height = ConvertUtils.dp2px(5);
            if (0 != i) {
                layoutParams.leftMargin = ConvertUtils.dp2px(10);
            }
            ImageView index = new ImageView(getContext());
            index.setLayoutParams(layoutParams);
            index.setScaleType(ImageView.ScaleType.FIT_XY);
            index.setImageDrawable(getResources().getDrawable(R.drawable.icon_img_index));
            index.setSelected(false);
            binding.photoviewLinlayNumber.addView(index);
        }
    }
}
