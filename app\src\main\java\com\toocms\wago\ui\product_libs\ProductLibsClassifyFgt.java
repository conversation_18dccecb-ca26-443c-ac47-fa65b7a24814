package com.toocms.wago.ui.product_libs;

import android.view.View;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtProductLibsClassifyBinding;

public class ProductLibsClassifyFgt extends BaseFragment<FgtProductLibsClassifyBinding, ProductLibsClassifyModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_product_libs_classify;
    }

    @Override
    public int getVariableId() {
        return BR.productLibsClassifyModel;
    }

    @Override
    protected ProductLibsClassifyModel getViewModel() {
        return new ProductLibsClassifyModel(TooCMSApplication.getInstance(), getArguments().getString("categoryId"), getArguments().getString("categoryName"));
    }

    @Override
    protected void viewObserver() {

    }
}
