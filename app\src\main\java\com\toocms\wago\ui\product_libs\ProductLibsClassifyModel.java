package com.toocms.wago.ui.product_libs;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.LanguageBean;
import com.toocms.wago.bean.ProductBean;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.bean.ProductManualBean;

public class ProductLibsClassifyModel extends BaseViewModel<BaseModel> {

    public ObservableArrayList<ProductLibsSecondaryClassifyModel> secondaryClassifyItems = new ObservableArrayList<>();
    public ItemBinding<ProductLibsSecondaryClassifyModel> secondaryClassifyItemBinding = ItemBinding.of(BR.productLibsSecondaryClassifyModel, R.layout.listitem_product_libs_secondary_classify);
    public ObservableArrayList<ProductLibsSmallClassifyModel> smallClassifyItems = new ObservableArrayList<>();
    public ItemBinding<ProductLibsSmallClassifyModel> smallClassifyItemBinding = ItemBinding.of(BR.productLibsSmallClassifyModel, R.layout.listitem_product_libs_small_classify);

    public ObservableField<String> classify = new ObservableField<>();
    public ObservableField<String> smallClassify = new ObservableField<>();
    public String categoryId;

    public ProductLibsClassifyModel(@NonNull Application application, String categoryId, String categoryName) {
        super(application);
        this.categoryId = categoryId;
        classify.set(categoryName);
        selectByProductClass();
    }

    private void selectByProductClass() {
        ApiTool.get("product/selectByProductClass")
                .add("parentCategoryId", categoryId)
                .asTooCMSResponseList(ProductClassBean.class)
                .withViewModel(this)
                .request(productClassBeans -> {
                    CollectionUtils.forAllDo(productClassBeans, (index, item) -> secondaryClassifyItems.add(new ProductLibsSecondaryClassifyModel(this, item)));
                    if (CollectionUtils.isNotEmpty(secondaryClassifyItems))
                        secondaryClassifyItems.get(0).onClickBindingCommand.execute();
                });
    }

    public void selectByProductKu(String categoryId) {
        ApiTool.get("product/selectByProductKu")
                .add("parentCategoryId", categoryId)
                .asTooCMSResponseList(ProductBean.class)
                .withViewModel(this)
                .request(productBeans -> {
                    smallClassifyItems.clear();
                    CollectionUtils.forAllDo(productBeans, (index, item) -> {
                        smallClassifyItems.add(new ProductLibsSmallClassifyModel(this, item));
                    });
                });
    }
}
