package com.toocms.wago.ui.product_libs;

import android.view.View;

import androidx.fragment.app.FragmentTransaction;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtProductLibsBinding;

public class ProductLibsFgt extends BaseFragment<FgtProductLibsBinding, ProductLibsModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_product_libs;
    }

    @Override
    public int getVariableId() {
        return BR.productLibsModel;
    }

    @Override
    protected void viewObserver() {
        this.viewModel.showContent.observe(this, fgt -> {
            FragmentTransaction fragmentTransaction = getChildFragmentManager().beginTransaction();
            if (fragmentTransaction.isEmpty()) {
                fragmentTransaction.add(R.id.content_fl, fgt);
            } else {
                fragmentTransaction.replace(R.id.content_fl, fgt);
            }
            fragmentTransaction.commit();
        });
    }
}
