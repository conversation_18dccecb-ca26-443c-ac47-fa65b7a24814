package com.toocms.wago.ui.product_libs;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.fragment.app.Fragment;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.ui.search.SearchFgt;

public class ProductLibsModel extends BaseViewModel<BaseModel> {

    public ObservableArrayList<ProductLibsTopClassifyModel> topClassifyItems = new ObservableArrayList<>();
    public ItemBinding<ProductLibsTopClassifyModel> topClassifyItemBinding = ItemBinding.of(BR.productLibsTopClassifyModel, R.layout.listitem_product_libs_top_classify);

    public SingleLiveEvent<Fragment> showContent = new SingleLiveEvent<>();

    public ProductLibsModel(@NonNull Application application) {
        super(application);
        selectByProductClass();
//        topClassifyItems.add(new ProductLibsTopClassifyModel(this, true));
    }

    public BindingCommand onSearchClickBindingCommand = new BindingCommand(() -> {
        startFragment(SearchFgt.class);
    });

    private void selectByProductClass() {
        ApiTool.get("product/selectByProductClass")
                .asTooCMSResponseList(ProductClassBean.class)
                .withViewModel(this)
                .request(productClassBeans -> {
                    topClassifyItems.add(new ProductLibsTopClassifyModel(this));
                    CollectionUtils.forAllDo(productClassBeans, (index, item) -> topClassifyItems.add(new ProductLibsTopClassifyModel(this, item)));
//                    topClassifyItems.add(new ProductLibsTopClassifyModel(this, "WAGO SCADA"));
                    if (CollectionUtils.isNotEmpty(topClassifyItems))
                        topClassifyItems.get(0).onClickBindingCommand.execute();
                });
    }
}
