package com.toocms.wago.ui.product_libs;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtProductLibsNewestBinding;

public class ProductLibsNewestFgt extends BaseFragment<FgtProductLibsNewestBinding, ProductLibsNewestModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_product_libs_newest;
    }

    @Override
    public int getVariableId() {
        return BR.productLibsNewestModel;
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
