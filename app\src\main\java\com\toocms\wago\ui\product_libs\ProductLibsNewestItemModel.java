package com.toocms.wago.ui.product_libs;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.NewestBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

public class ProductLibsNewestItemModel extends ItemViewModel<ProductLibsNewestModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> name = new ObservableField<>();
    public ObservableField<String> sub = new ObservableField<>();
    public ObservableField<String> type = new ObservableField<>();
    public String productId;

    public ProductLibsNewestItemModel(@NonNull ProductLibsNewestModel viewModel, NewestBean.RowsBean rowsBean) {
        super(viewModel);
        productId = rowsBean.productId;
        url.set(rowsBean.thumbnailUrl);
        name.set(rowsBean.totalTitle);
        sub.set(rowsBean.subhead);
        type.set(rowsBean.productType);
    }

    public BindingCommand onItemClickBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
        bundle.putString("productId", productId);
        bundle.putString("productName", name.get());
        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
