package com.toocms.wago.ui.product_libs;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.NewestBean;
import com.toocms.wago.bean.ProductManualBean;

public class ProductLibsNewestModel extends BaseViewModel<BaseModel> {

    public ObservableArrayList<ProductLibsNewestItemModel> items = new ObservableArrayList<>();
    public ItemBinding<ProductLibsNewestItemModel> itemBinding = ItemBinding.of(BR.productLibsNewestItemModel, R.layout.listitem_product_libs_newest);

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public int p = 1;

    public ProductLibsNewestModel(@NonNull Application application) {
        super(application);
        selectByNewProduct(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByNewProduct(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByNewProduct(false);
    });

    public void selectByNewProduct(boolean isShowLoading) {
        ApiTool.postJson("product/selectByNewProduct")
                .add("currentPage", p)
                .add("pageSize", 0)
                .add("query", "")
                .asTooCMSResponse(NewestBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(newestBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(newestBean.rows, (index, item) -> {
                        items.add(new ProductLibsNewestItemModel(this, item));
                    });
                });
    }
}
