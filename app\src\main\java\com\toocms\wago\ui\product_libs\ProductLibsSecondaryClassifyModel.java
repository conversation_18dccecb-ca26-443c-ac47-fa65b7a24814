package com.toocms.wago.ui.product_libs;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.wago.bean.LanguageBean;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.config.Constants;

public class ProductLibsSecondaryClassifyModel extends ItemViewModel<ProductLibsClassifyModel> {

    public ObservableField<String> smallClassify = new ObservableField<>();
    public ObservableField<Boolean> isSelected = new ObservableField<>();
    public String classifyId;

    public BindingCommand onClickBindingCommand = new BindingCommand(() -> {
        Messenger.getDefault().send(this, Constants.MESSENGER_TOKEN_SECONDARY_CLASSIFY_CLICK);
        viewModel.smallClassify.set(smallClassify.get());
        viewModel.selectByProductKu(classifyId);
    });

    public ProductLibsSecondaryClassifyModel(@NonNull ProductLibsClassifyModel viewModel, ProductClassBean productClassBean) {
        super(viewModel);
        smallClassify.set(productClassBean.productCategoryName);
        classifyId = productClassBean.productCategoryId;
        registerSecondaryClassifyClickMessenger();
    }

    private void registerSecondaryClassifyClickMessenger() {
        Messenger.getDefault().register(this, Constants.MESSENGER_TOKEN_SECONDARY_CLASSIFY_CLICK, ProductLibsSecondaryClassifyModel.class, itemViewModel -> {
            isSelected.set(this == itemViewModel);
        });
    }
}
