package com.toocms.wago.ui.product_libs;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ProductBean;
import com.toocms.wago.bean.ProductManualBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

public class ProductLibsSmallClassifyModel extends ItemViewModel<ProductLibsClassifyModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> type = new ObservableField<>();
    public ObservableField<String> name = new ObservableField<>();
    public String productId;

    public ProductLibsSmallClassifyModel(@NonNull ProductLibsClassifyModel viewModel, ProductBean productBean) {
        super(viewModel);
        productId = productBean.productId;
        url.set(productBean.thumbnailUrl);
        name.set(productBean.totalTitle);
        type.set(productBean.productType);
    }

    public BindingCommand onItemClickBindingCommand = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
        bundle.putString("productId", productId);
        bundle.putString("productName", name.get());
        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
