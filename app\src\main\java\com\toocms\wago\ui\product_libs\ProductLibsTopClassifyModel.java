package com.toocms.wago.ui.product_libs;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.Messenger;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.web.WebFgt;

public class ProductLibsTopClassifyModel extends ItemViewModel<ProductLibsModel> {

    private boolean isNewest;
    public ObservableField<Boolean> isSelected = new ObservableField<>();
    public ObservableField<String> product = new ObservableField<>();
    public String categoryId;

    public BindingCommand onClickBindingCommand = new BindingCommand(() -> {
//        if (product.get().equals("WAGO SCADA")) {
//            Bundle bundle = new Bundle();
//            bundle.putString("url", "http://www.scadaforweb.com");
//            viewModel.startFragment(WebFgt.class, bundle);
//            return;
//        }
        Messenger.getDefault().send(this, Constants.MESSENGER_TOKEN_TOP_CLASSIFY_CLICK);
        if (isNewest) {
            viewModel.showContent.setValue(new ProductLibsNewestFgt());
        } else {
            ProductLibsClassifyFgt fgt = new ProductLibsClassifyFgt();
            Bundle bundle = new Bundle();
            bundle.putString("categoryName", product.get());
            bundle.putString("categoryId", categoryId);
            fgt.setArguments(bundle);
            viewModel.showContent.setValue(fgt);
        }
    });

    public ProductLibsTopClassifyModel(@NonNull ProductLibsModel viewModel, ProductClassBean productClassBean) {
        super(viewModel);
        product.set(productClassBean.productCategoryName);
        categoryId = productClassBean.productCategoryId;
        registerTopClassifyClickMessenger();
        isNewest = false;
    }

    public ProductLibsTopClassifyModel(@NonNull ProductLibsModel viewModel) {
        super(viewModel);
        product.set("新产品");
        registerTopClassifyClickMessenger();
        this.isNewest = true;
    }

    public ProductLibsTopClassifyModel(@NonNull ProductLibsModel viewModel, String product) {
        super(viewModel);
        this.product.set(product);
//        registerTopClassifyClickMessenger();
//        this.isNewest = true;
    }

    private void registerTopClassifyClickMessenger() {
        Messenger.getDefault().register(this, Constants.MESSENGER_TOKEN_TOP_CLASSIFY_CLICK, ProductLibsTopClassifyModel.class, itemViewModel -> {
            isSelected.set(this == itemViewModel);
        });
    }
}
