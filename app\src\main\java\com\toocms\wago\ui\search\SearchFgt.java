package com.toocms.wago.ui.search;

import android.view.View;

import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtSearchBinding;

public class SearchFgt extends BaseFragment<FgtSearchBinding, SearchModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_search;
    }

    @Override
    public int getVariableId() {
        return BR.searchModel;
    }

    @Override
    protected void viewObserver() {

    }
}
