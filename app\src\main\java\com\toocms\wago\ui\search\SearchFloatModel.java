package com.toocms.wago.ui.search;

import android.os.Bundle;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

import java.util.List;

public class SearchFloatModel extends MultiItemViewModel<SearchModel> {

    public ObservableArrayList<View> items = new ObservableArrayList<>();

    public SearchFloatModel(@NonNull SearchModel viewModel, List<View> items) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_TWO);
        if (null != items && !items.isEmpty()) {
            this.items.addAll(items);
        }
    }
}
