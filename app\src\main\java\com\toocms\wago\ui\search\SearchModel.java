package com.toocms.wago.ui.search;

import android.app.Application;
import android.graphics.drawable.ColorDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.blankj.utilcode.util.ColorUtils;
import com.blankj.utilcode.util.ConvertUtils;
import com.blankj.utilcode.util.LogUtils;
import com.blankj.utilcode.util.ResourceUtils;
import com.blankj.utilcode.util.StringUtils;
import com.bumptech.glide.Glide;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.BannerBean;
import com.toocms.wago.bean.HotSearchBean;
import com.toocms.wago.bean.ProductClassBean;
import com.toocms.wago.history.Constants;
import com.toocms.wago.history.History;
import com.toocms.wago.ui.product_libs.ProductLibsTopClassifyModel;

import java.util.ArrayList;
import java.util.List;

public class SearchModel extends BaseViewModel<BaseModel> {

    public History<String> history;
    public ObservableField<String> keyword = new ObservableField<>();
    public SearchTitleModel searchTitleModel;
    public SearchFloatModel searchFloatModel;

    public ObservableArrayList<MultiItemViewModel> items = new ObservableArrayList<>();
    public ItemBinding<MultiItemViewModel> itemBinding = ItemBinding.of((binding, position, item) -> {
        if (item instanceof SearchTitleModel) {
            binding.set(BR.searchTitleModel, R.layout.layout_search_title);
        } else if (item instanceof SearchFloatModel) {
            binding.set(BR.searchFloatModel, R.layout.layout_search_float);
        }
    });

    public BindingCommand onBackBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public BindingCommand onSearchBindingCommand = new BindingCommand(() -> {
        saveHistory(keyword.get());
        Bundle bundle = new Bundle();
        bundle.putString("keyword", keyword.get());
        startFragment(SearchResultFgt.class, bundle, true);
    });

    public SearchModel(@NonNull Application application) {
        super(application);
        history = new History<>(application, Constants.KEY_HISTORY, String.class)
                .setOnSaveResultListener(isSucceed -> {
                    if (isSucceed) history.getHistory();
                })
                .setOnDeleteResultListener(isSucceed -> {
                    if (isSucceed) history.getHistory();
                })
                .setOnSearchResultListener((key, historyList) -> {
                    if (null == historyList || historyList.isEmpty()) {
                        if (CollectionUtils.isNotEmpty(items)) {
                            items.remove(searchTitleModel);
                            items.remove(searchFloatModel);
                        }
                        return;
                    }
                    if (CollectionUtils.isEmpty(items)) {
                        searchTitleModel = new SearchTitleModel(this, StringUtils.getString(R.string.str_search_history), true);
                        items.add(0, searchTitleModel);
                        List<View> history = new ArrayList<>();
                        CollectionUtils.forAllDo(historyList, (index, item) -> {
                            history.add(historyItemView(item));
                        });
                        searchFloatModel = new SearchFloatModel(this, history);
                        items.add(1, searchFloatModel);
                    } else {
                        SearchTitleModel searchTitleModel = (SearchTitleModel) items.get(0);
                        if (searchTitleModel.title.equals(StringUtils.getString(R.string.str_search_history))) {
                            this.searchTitleModel = searchTitleModel;
                            List<View> history = new ArrayList<>();
                            CollectionUtils.forAllDo(historyList, (index, item) -> {
                                history.add(historyItemView(item));
                            });
                            searchFloatModel = new SearchFloatModel(this, history);
                            items.set(1, searchFloatModel);
                        }
                    }
                });
        history.getHistory();
        selectEnabledHotSearch();
    }

    /**
     * 保存历史
     *
     * @param history
     */
    public void saveHistory(String history) {
        this.history.saveHistory(history);
    }

    /**
     * 删除历史
     */
    public void deleteHistory() {
        this.history.deleteHistory();
    }

    private View historyItemView(String history) {
        TextView result = new TextView(getApplication());
        result.setPadding(ConvertUtils.dp2px(10), ConvertUtils.dp2px(5), ConvertUtils.dp2px(10), ConvertUtils.dp2px(5));
        result.setBackground(ResourceUtils.getDrawable(R.drawable.shape_sol_00000000_str_eef0f2_cor_25dp));
        result.setGravity(Gravity.CENTER);
        result.setMinWidth(ConvertUtils.dp2px(50));
        result.setText(history);
        result.setOnClickListener(view -> {
            Bundle bundle = new Bundle();
            bundle.putString("keyword", history);
            startFragment(SearchResultFgt.class, bundle, true);
        });
        return result;
    }

    private View hotItemView(String history) {
        TextView result = new TextView(getApplication());
        result.setPadding(ConvertUtils.dp2px(10), ConvertUtils.dp2px(5), ConvertUtils.dp2px(10), ConvertUtils.dp2px(5));
        result.setBackground(ResourceUtils.getDrawable(R.drawable.shape_sol_clr_bg_cor_25dp));
        result.setCompoundDrawablePadding(ConvertUtils.dp2px(5));
        Drawable drawable = ResourceUtils.getDrawable(R.mipmap.icon_hot);
        drawable.setBounds(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
        result.setCompoundDrawables(drawable, null, null, null);
        result.setGravity(Gravity.CENTER);
        result.setMinWidth(ConvertUtils.dp2px(50));
        result.setText(history);
        result.setOnClickListener(view -> {
            Bundle bundle = new Bundle();
            bundle.putString("keyword", history);
            startFragment(SearchResultFgt.class, bundle, true);
        });
        return result;
    }

    private void selectEnabledHotSearch() {
        ApiTool.post("system/hotSearch/selectEnabledHotSearch")
                .asTooCMSResponseList(HotSearchBean.class)
                .withViewModel(this)
                .request(hotSearchBeans -> {
                    List<View> hot = new ArrayList<>();
                    CollectionUtils.forAllDo(hotSearchBeans, (index, item) -> hot.add(hotItemView(item.trendingSearch)));
                    items.add(new SearchTitleModel(this, StringUtils.getString(R.string.str_hot_search), false));
                    items.add(new SearchFloatModel(this, hot));
                });
    }
}
