package com.toocms.wago.ui.search;

import android.view.View;

import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtSearchResultBinding;

public class SearchResultFgt extends BaseFragment<FgtSearchResultBinding, SearchResultModel> {

    @Override
    protected void onFragmentCreated() {
        topBar.setVisibility(View.GONE);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_search_result;
    }

    @Override
    public int getVariableId() {
        return BR.searchResultModel;
    }

    @Override
    protected SearchResultModel getViewModel() {
        return new SearchResultModel(TooCMSApplication.getInstance(), getArguments().getString("keyword"));
    }

    @Override
    protected void viewObserver() {
        viewModel.onRefreshFinish.observe(this, v -> binding.refresh.finishRefresh());
        viewModel.onLoadMoreFinish.observe(this, v -> binding.refresh.finishLoadMore());
    }
}
