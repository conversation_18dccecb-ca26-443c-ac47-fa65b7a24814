package com.toocms.wago.ui.search;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.ItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.bean.ProductBean;
import com.toocms.wago.config.Constants;
import com.toocms.wago.ui.details.DetailsFgt;

public class SearchResultItemModel extends ItemViewModel<SearchResultModel> {

    public ObservableField<String> url = new ObservableField<>();
    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<String> type = new ObservableField<>();
    public ObservableField<String> subTitle = new ObservableField<>();
    public String productId;

    public SearchResultItemModel(@NonNull SearchResultModel viewModel, ProductBean productBean) {
        super(viewModel);
        productId = productBean.productId;
        url.set(productBean.thumbnailUrl);
        title.set(productBean.totalTitle);
        type.set(productBean.productType);
        subTitle.set(productBean.subhead);
    }

    public BindingCommand detail = new BindingCommand(() -> {
        Bundle bundle = new Bundle();
        bundle.putString("detailType", Constants.DETAIL_TYPE_PRODUCT);
        bundle.putString("productId", productId);
        bundle.putString("productName", title.get());
        viewModel.startFragment(DetailsFgt.class, bundle);
    });
}
