package com.toocms.wago.ui.search;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableArrayList;
import androidx.databinding.ObservableField;

import com.blankj.utilcode.util.CollectionUtils;
import com.google.gson.JsonObject;
import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.binding.ItemBinding;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.bus.event.SingleLiveEvent;
import com.toocms.tab.network.ApiTool;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.bean.CategoryAndProductBean;
import com.toocms.wago.bean.ProductListBean;
import com.toocms.wago.config.UserRepository;
import com.toocms.wago.ui.module.ModuleContentItemModel;

public class SearchResultModel extends BaseViewModel<BaseModel> {

    public int p = 1;
    public ObservableField<String> keyword = new ObservableField<>();

    public SingleLiveEvent<Void> onRefreshFinish = new SingleLiveEvent<>();
    public SingleLiveEvent<Void> onLoadMoreFinish = new SingleLiveEvent<>();

    public ObservableArrayList<SearchResultItemModel> items = new ObservableArrayList<>();
    public ItemBinding<SearchResultItemModel> itemBinding = ItemBinding.of(BR.searchResultItemModel, R.layout.listitem_search_result);

    public BindingCommand onBackClickBindingCommand = new BindingCommand(() -> {
        finishFragment();
    });

    public BindingCommand onSearchClickBindingCommand = new BindingCommand(() -> {
        selectByPageAndCondition(true);
    });

    public SearchResultModel(@NonNull Application application, String keyword) {
        super(application);
        this.keyword.set(keyword);
        selectByPageAndCondition(true);
    }

    public BindingCommand onRefreshCommand = new BindingCommand(() -> {
        p = 1;
        selectByPageAndCondition(false);
    });

    public BindingCommand onLoadMoreCommand = new BindingCommand(() -> {
        ++p;
        selectByPageAndCondition(false);
    });

    public void selectByPageAndCondition(boolean isShowLoading) {
        JsonObject query = new JsonObject();
        query.addProperty("additionalFunctionId", "");
        query.addProperty("categoryId1", "");
        query.addProperty("categoryId2", "");
        query.addProperty("userid", UserRepository.getInstance().getUser().id);
        query.addProperty("functionId", "");
        query.addProperty("typeId", "");
        query.addProperty("totalTitle", keyword.get());
        ApiTool.postJson("product/selectByPageAndCondition")
                .add("currentPage", p)
                .add("pageSize", 0)
                .addJsonElement("query", query.toString())
                .asTooCMSResponse(ProductListBean.class)
                .withViewModel(this)
                .showLoading(isShowLoading)
                .request(productListBean -> {
                    if (p == 1) {
                        items.clear();
                        onRefreshFinish.call();
                    } else onLoadMoreFinish.call();
                    CollectionUtils.forAllDo(productListBean.rows, (index, item) -> {
                        items.add(new SearchResultItemModel(this, item));
                    });
                });
    }
}
