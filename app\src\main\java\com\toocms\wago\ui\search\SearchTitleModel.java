package com.toocms.wago.ui.search;

import androidx.annotation.NonNull;
import androidx.databinding.ObservableField;

import com.toocms.tab.base.BaseModel;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.tab.base.MultiItemViewModel;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.wago.config.Constants;

public class SearchTitleModel extends MultiItemViewModel<SearchModel> {

    public ObservableField<String> title = new ObservableField<>();
    public ObservableField<Boolean> isShowClear = new ObservableField<>();

    public SearchTitleModel(@NonNull SearchModel viewModel, String title, boolean isShowClear) {
        super(viewModel);
        setItemType(Constants.RECYCLER_VIEW_ITEM_TYPE_ONE);
        this.title.set(title);
        this.isShowClear.set(isShowClear);
    }

    public BindingCommand onClearBindingCommand = new BindingCommand(() -> {
        viewModel.deleteHistory();
    });
}
