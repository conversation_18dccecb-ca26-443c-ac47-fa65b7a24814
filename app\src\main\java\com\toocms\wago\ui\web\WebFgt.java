package com.toocms.wago.ui.web;

import android.annotation.SuppressLint;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import com.qmuiteam.qmui.widget.webview.QMUIWebViewClient;
import com.toocms.tab.TooCMSApplication;
import com.toocms.tab.base.BaseFragment;
import com.toocms.tab.base.BaseViewModel;
import com.toocms.wago.BR;
import com.toocms.wago.R;
import com.toocms.wago.databinding.FgtWebBinding;

/**
 * Author：Zero
 * Date：2021/7/9
 */
public class WebFgt extends BaseFragment<FgtWebBinding, BaseViewModel> {

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onFragmentCreated() {
        String url = getArguments().getString("url");
        WebSettings settings = binding.webview.getSettings();
        settings.setJavaScriptEnabled(true);
        binding.webview.setWebViewClient(new AdvertWebViewClient());
        binding.webview.loadUrl(url);
    }

    @Override
    protected int getLayoutResId() {
        return R.layout.fgt_web;
    }

    @Override
    public int getVariableId() {
        return 0;
    }

    @Override
    protected void viewObserver() {
    }

    @Override
    protected void onBackPressed() {
        if (binding.webview.canGoBack()) {
            binding.webview.goBack();
        } else
            super.onBackPressed();
    }

    public class AdvertWebViewClient extends QMUIWebViewClient {

        public AdvertWebViewClient() {
            super(true, true);
        }

        @Override
        public void onPageFinished(WebView view, String url) {
            super.onPageFinished(view, url);
            topBar.setTitle(view.getTitle());
        }
    }
}
