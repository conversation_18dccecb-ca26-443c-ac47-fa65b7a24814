package com.toocms.wago.widget;

import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;

import com.blankj.utilcode.util.CollectionUtils;
import com.toocms.tab.imageload.ImageLoader;
import com.toocms.tab.widget.banner.base.BaseIndicatorBanner;
import com.toocms.wago.bean.ProductDetailBean;

import cn.jzvd.JzvdStd;

/**
 * Author：Zero
 * Date：2021/7/27
 */
public class VideoBanner extends BaseIndicatorBanner<ProductDetailBean.ProductBean.BannerListBean, VideoBanner> {

    public VideoBanner(Context context, AttributeSet attrs) {
        super(context, attrs);
        this.setBarShowWhenLast(true);
        this.setAutoScrollEnable(false);
    }

    @Override
    public View onCreateItemView(int position) {
        FrameLayout frameLayout = new FrameLayout(getContext());
        if (CollectionUtils.isEmpty(mDatas)) return frameLayout;
        ProductDetailBean.ProductBean.BannerListBean bannerListBean = mDatas.get(position);
        if (bannerListBean.isImg) {
            ImageView imageView = new ImageView(getContext());
            frameLayout.addView(imageView);
            ImageLoader.loadUrl2Image(bannerListBean.bannerUrl, imageView, 0);
        } else {
            JzvdStd jzvdStd = new JzvdStd(getContext());
            frameLayout.addView(jzvdStd);
            jzvdStd.setUp(bannerListBean.bannerUrl, "");
            ImageLoader.loadUrl2Image(bannerListBean.bannerUrl, jzvdStd.posterImageView, 0);
        }
        return frameLayout;
    }
}
