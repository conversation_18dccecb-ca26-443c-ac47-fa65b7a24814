package com.toocms.wago.widget;

import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.View;
import android.webkit.WebView;
import android.widget.EditText;

import androidx.databinding.BindingAdapter;

import com.qmuiteam.qmui.widget.webview.QMUIWebView;
import com.toocms.tab.binding.command.BindingCommand;
import com.toocms.tab.widget.banner.BannerItem;
import com.toocms.tab.widget.banner.SimpleImageBanner;
import com.toocms.tab.widget.banner.base.BaseBanner;
import com.toocms.tab.widget.navigation.FlipNavigationView;
import com.toocms.tab.widget.navigation.NavigationItem;
import com.toocms.wago.bean.ProductDetailBean;

import java.util.List;

/**
 * Author：Zero
 * Date：2021/6/16
 */
public class ViewAdapter {

    @BindingAdapter(value = {"onKey"}, requireAll = false)
    public static void onKeyCommand(EditText editText, BindingCommand<Integer> command) {
        editText.setOnKeyListener((view, keyCode, keyEvent) -> {
            command.execute(keyCode);
            return false;
        });
    }

    @BindingAdapter({"loadUrl"})
    public static void loadUrl(QMUIWebView webView, final String loadUrl) {
        if (!TextUtils.isEmpty(loadUrl)) {
            webView.loadUrl(loadUrl);
        }
    }

    @BindingAdapter(value = {"items", "onItemClickListener"}, requireAll = false)
    public static void setAdapter(VideoBanner videoBanner,
                                  List<ProductDetailBean.ProductBean.BannerListBean> items,
                                  BaseBanner.OnItemClickListener<ProductDetailBean.ProductBean.BannerListBean> onItemClickListener) {
        videoBanner.setSource(items);
        if (onItemClickListener != null)
            videoBanner.setOnItemClickListener(onItemClickListener);
        videoBanner.startScroll();
    }
}
