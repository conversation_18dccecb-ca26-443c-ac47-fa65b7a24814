package com.toocms.wago.widget.verify_edit_text;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Rect;
import android.graphics.Typeface;
import android.text.Editable;
import android.text.InputFilter;
import android.text.InputType;
import android.util.AttributeSet;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatEditText;

import com.blankj.utilcode.util.ConvertUtils;
import com.toocms.wago.R;

public class VerifyCodeEditText extends AppCompatEditText {

    public static final String TAG = VerifyCodeEditText.class.getSimpleName();
    private Paint paint = new Paint();
    private int verifyCodeLength = 4;
    private int backgroundColor = 0x00000000;
    private int borderColor = 0xFF999999;
    private int textColor = 0xFF999999;
    private int selectedColor = 0xFF999999;
    private int borderRadius = 0;
    private int textSize = 25; //字体颜色
    private int spacing = 25; //框的间距
    private boolean isShowIndicator = false;

    public VerifyCodeEditText(@NonNull Context context) {
        super(context);
        initializeEditText();
    }

    public VerifyCodeEditText(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        initializeAttributeSet(context, attrs);
        initializeEditText();
    }

    public VerifyCodeEditText(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initializeAttributeSet(context, attrs);
        initializeEditText();
    }

    private void initializeEditText() {
        paint.setAntiAlias(true);
        setInputType(InputType.TYPE_CLASS_NUMBER);
        setBackgroundColor(0x00FFFFFF);
        // 不显示光标
        setCursorVisible(false);
        setLongClickable(false);
        setFilters(new InputFilter[]{new InputFilter.LengthFilter(verifyCodeLength)});
    }

    private void initializeAttributeSet(Context context, AttributeSet attrs) {
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.VerifyCodeEditText);
        verifyCodeLength = typedArray.getInteger(R.styleable.VerifyCodeEditText_vcVerifyCodeLength, 4);
        backgroundColor = typedArray.getColor(R.styleable.VerifyCodeEditText_vcBackgroundColor, 0x00000000);
        borderColor = typedArray.getColor(R.styleable.VerifyCodeEditText_vcBorderColor, 0xFF999999);
        borderRadius = typedArray.getDimensionPixelSize(R.styleable.VerifyCodeEditText_vcBorderRadius, 0);
        spacing = typedArray.getDimensionPixelSize(R.styleable.VerifyCodeEditText_vcSpacing, 0);
        textSize = typedArray.getDimensionPixelSize(R.styleable.VerifyCodeEditText_vcTextSize, 25);
        textColor = typedArray.getColor(R.styleable.VerifyCodeEditText_vcTextColor, 0xFF999999);
        selectedColor = typedArray.getColor(R.styleable.VerifyCodeEditText_vcSelectedColor, 0xFF999999);
        isShowIndicator = typedArray.getBoolean(R.styleable.VerifyCodeEditText_vcShowIndicator, false);
        typedArray.recycle();
    }

    @Override
    protected void onDraw(Canvas canvas) {
//        super.onDraw(canvas);
        //绘制背景
        drawBackground(canvas);
        drawBorder(canvas);
        drawIndicator(canvas);
        drawText(canvas);
    }

    private void drawText(Canvas canvas) {
        paint.setStyle(Paint.Style.FILL);
        paint.setTextSize(textSize);
        float ascent = paint.ascent();
        float descent = paint.descent();

//        paint.setStrokeWidth(ConvertUtils.dp2px(1));
        int singleBorderWidth = (getWidth() - spacing * (verifyCodeLength - 1)) / verifyCodeLength;
        Editable text = getText();
        int textLength = text.length();
        for (int i = 0; i < textLength; i++) {
            if (i + 1 == getText().length()) {
                paint.setColor(selectedColor);
            } else {
                paint.setColor(textColor);
            }
            float currentTextWidth = paint.measureText(text, i, i + 1);
            canvas.drawText(text, i, i + 1, singleBorderWidth * i + spacing * i + singleBorderWidth / 2 - currentTextWidth / 2, getHeight() / 2 + (Math.abs(ascent) + Math.abs(descent)) / 2 - Math.abs(descent), paint);
        }
    }

    private void drawBorder(Canvas canvas) {
//        paint.setColor(borderColor);
        paint.setStyle(Paint.Style.STROKE);
        int width = ConvertUtils.dp2px(1);
        paint.setStrokeWidth(width);
        int singleBorderWidth = (getWidth() - spacing * (verifyCodeLength - 1)) / verifyCodeLength;
        for (int i = 0; i < verifyCodeLength; i++) {
            if (i + 1 == getText().length()) {
                paint.setColor(selectedColor);
            } else {
                paint.setColor(borderColor);
            }
            if (0 == borderRadius) {
                canvas.drawRect(singleBorderWidth * i + spacing * i + ((float) width / 2), ((float) width / 2), singleBorderWidth * i + spacing * i + singleBorderWidth - ((float) width / 2), getHeight() - ((float) width / 2), paint);
            } else {
                canvas.drawRoundRect(singleBorderWidth * i + spacing * i + ((float) width / 2), ((float) width / 2), singleBorderWidth * i + spacing * i + singleBorderWidth - ((float) width / 2), getHeight() - ((float) width / 2), borderRadius, borderRadius, paint);
            }
        }
    }

    private void drawBackground(Canvas canvas) {
        paint.setColor(backgroundColor);
        paint.setStyle(Paint.Style.FILL);
        int singleBorderWidth = (getWidth() - spacing * (verifyCodeLength - 1)) / verifyCodeLength;
        for (int i = 0; i < verifyCodeLength; i++) {
            if (0 == borderRadius) {
                canvas.drawRect(singleBorderWidth * i + spacing * i, 0, singleBorderWidth * i + spacing * i + singleBorderWidth, getHeight(), paint);
            } else {
                canvas.drawRoundRect(singleBorderWidth * i + spacing * i, 0, singleBorderWidth * i + spacing * i + singleBorderWidth, getHeight(), borderRadius, borderRadius, paint);
            }
        }
    }

    private void drawIndicator(Canvas canvas) {
        if (!isShowIndicator) return;
        int length = getText().length();
        if (1 > length) return;
        paint.setColor(selectedColor);
        int width = ConvertUtils.dp2px(1);
        paint.setStrokeWidth(width);
        paint.setStyle(Paint.Style.FILL);
        int singleBorderWidth = (getWidth() - spacing * (verifyCodeLength - 1)) / verifyCodeLength;
        float borderLeft = singleBorderWidth * (length - 1) + spacing * (length - 1); //边框左边位置
        float borderRight = borderLeft + singleBorderWidth; //边框右边位置
        float indicatorWidth = (float) singleBorderWidth * 6 / 10;
        canvas.drawLine(borderLeft + (singleBorderWidth - indicatorWidth) / 2, getHeight() * 9 / 10, borderRight - (singleBorderWidth - indicatorWidth) / 2, getHeight() * 9 / 10, paint);
    }
}
