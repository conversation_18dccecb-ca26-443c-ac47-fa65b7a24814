<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="aboutUsModel"
            type="com.toocms.wago.ui.about_us.AboutUsModel" />
    </data>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="1dp">

            <ImageView
                android:id="@+id/imageview"
                android:layout_width="match_parent"
                android:layout_height="270dp"
                android:layout_marginTop="10dp"
                android:scaleType="fitXY"
                android:src="@mipmap/icon_about_us"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.cardview.widget.CardView
                android:id="@+id/cv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:background="@color/clr_bg"
                app:cardBackgroundColor="@color/clr_bg"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintTop_toBottomOf="@id/imageview">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="15dp"
                    app:onClickCommand="@{aboutUsModel.startWagoDetail}">

                    <View
                        android:id="@+id/view0"
                        android:layout_width="5dp"
                        android:layout_height="0dp"
                        android:layout_marginLeft="30dp"
                        android:background="@drawable/shape_sol_clr_main_cor_10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="H,1:1"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                        android:id="@+id/imageview2"
                        android:layout_width="65dp"
                        android:layout_height="65dp"
                        android:layout_marginLeft="15dp"
                        android:src="@mipmap/icon_flag_wago"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/view0"
                        app:layout_constraintTop_toTopOf="parent"
                        app:qmui_border_color="@color/clr_transparent"
                        app:qmui_corner_radius="8dp" />

                    <TextView
                        android:id="@+id/text1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="10dp"
                        android:text="关于万可"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toRightOf="@id/imageview2"
                        app:layout_constraintTop_toTopOf="@id/imageview2" />

                    <TextView
                        android:id="@+id/text2"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="12dp"
                        android:text="WAGO  GROUP"
                        android:textColor="#A5A8B1"
                        android:textSize="12sp"
                        app:layout_constraintLeft_toRightOf="@id/imageview2"
                        app:layout_constraintTop_toBottomOf="@id/text1" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="30dp"
                        android:src="@mipmap/icon_arrow_more"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/cv2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@color/clr_bg"
                app:cardBackgroundColor="@color/clr_bg"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintTop_toBottomOf="@id/cv">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/cl2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:padding="15dp"
                    app:layout_constraintTop_toBottomOf="@id/cl1"
                    app:onClickCommand="@{aboutUsModel.startServicesList}">

                    <View
                        android:id="@+id/view1"
                        android:layout_width="5dp"
                        android:layout_height="0dp"
                        android:layout_marginLeft="30dp"
                        android:background="@drawable/shape_sol_clr_main_cor_10dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="H,1:1"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                        android:id="@+id/imageview3"
                        android:layout_width="65dp"
                        android:layout_height="65dp"
                        android:layout_marginLeft="15dp"
                        android:src="@mipmap/icon_flag_wago"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/view1"
                        app:layout_constraintTop_toTopOf="parent"
                        app:qmui_border_color="@color/clr_transparent"
                        app:qmui_corner_radius="8dp" />

                    <TextView
                        android:id="@+id/text3"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="10dp"
                        android:text="客户服务"
                        android:textSize="12sp"
                        android:textStyle="bold"
                        app:layout_constraintLeft_toRightOf="@id/imageview3"
                        app:layout_constraintTop_toTopOf="@id/imageview3" />

                    <TextView
                        android:id="@+id/text4"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginLeft="20dp"
                        android:layout_marginTop="12dp"
                        android:text="CUSTOMER  SERVISE "
                        android:textColor="#A5A8B1"
                        android:textSize="12sp"
                        app:layout_constraintLeft_toRightOf="@id/imageview3"
                        app:layout_constraintTop_toBottomOf="@id/text3" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginRight="30dp"
                        android:src="@mipmap/icon_arrow_more"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="15dp"
                android:background="@color/clr_bg"
                app:cardBackgroundColor="@color/clr_bg"
                app:cardCornerRadius="10dp"
                app:cardElevation="0dp"
                app:layout_constraintTop_toBottomOf="@id/cv2"
                app:onClickCommand="@{aboutUsModel.call}">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:drawablePadding="8dp"
                    android:padding="8dp"
                    android:text="联系我们"
                    app:drawableStartCompat="@mipmap/flag_call" />
            </androidx.cardview.widget.CardView>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</layout>