<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="changePhoneViewModel"
            type="com.toocms.wago.ui.mine.settings.change_phone.ChangePhoneViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <TextView
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:paddingStart="30dp"
            android:text="@{changePhoneViewModel.tips}"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cll1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/text">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llc0"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="50dp"
                android:paddingEnd="50dp"
                app:layout_constraintTop_toTopOf="parent">

                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@color/clr_transparent"
                    android:hint="@{changePhoneViewModel.hint1}"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="@={changePhoneViewModel.phone}" />

                <TextView
                    isVisible="@{changePhoneViewModel.isPhone}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="@{changePhoneViewModel.clickable}"
                    android:text="@{changePhoneViewModel.countdown}"
                    app:onClickCommand="@{changePhoneViewModel.onCodeBindingCommand}" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#A4A8B0"
                app:layout_constraintTop_toBottomOf="@id/llc0" />

            <EditText
                android:id="@+id/edit2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@color/clr_transparent"
                android:hint="@{changePhoneViewModel.hint2}"
                android:paddingStart="50dp"
                android:paddingTop="10dp"
                android:paddingEnd="50dp"
                android:paddingBottom="10dp"
                android:password="true"
                android:text="@={changePhoneViewModel.code}"
                app:layout_constraintTop_toBottomOf="@id/llc0" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            isVisible="@{changePhoneViewModel.pageType.equals(changePhoneViewModel.PAGE_TYPE_OLD)  &amp;&amp; changePhoneViewModel.isPhone}"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginRight="30dp"
            android:text="当前手机号不可用？"
            android:textColor="#FF0000"
            android:textSize="12sp"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cll1"
            app:onClickCommand="@{changePhoneViewModel.onChangeBindingCommand}" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/TooCMS.RoundButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="50dp"
            android:layout_marginTop="200dp"
            android:layout_marginEnd="50dp"
            android:text="@{changePhoneViewModel.submit}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cll1"
            app:onClickCommand="@{changePhoneViewModel.onSubmitBindingCommand}"
            app:qmui_radius="25dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>