<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="checkLoginVerifyCodeModel"
            type="com.toocms.wago.ui.login.verify_code_login.CheckLoginVerifyCodeModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/image0"
                    android:layout_width="70dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="70dp"
                    android:layout_marginRight="50dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/icon_logo"
                    app:layout_constraintDimensionRatio="H,14:5"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="45dp"
                    android:text="@string/str_input_verify_code_hint"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/image0" />

                <TextView
                    android:id="@+id/send_phone_code_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="15dp"
                    android:text="@string/str_input_phone_code_hint"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text0" />

                <com.toocms.wago.widget.verify_edit_text.VerifyCodeEditText
                    android:id="@+id/verify_code_edt"
                    android:layout_width="0dp"
                    android:layout_height="65dp"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="45dp"
                    android:layout_marginRight="50dp"
                    android:text="@={checkLoginVerifyCodeModel.code}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/send_phone_code_tv"
                    app:vcBorderColor="#A4A8B0"
                    app:vcBorderRadius="10dp"
                    app:vcSelectedColor="@color/clr_main"
                    app:vcShowIndicator="true"
                    app:vcSpacing="15dp"
                    app:vcTextColor="#000000"
                    app:vcTextSize="45sp"
                    app:vcVerifyCodeLength="6" />

                <TextView
                    android:id="@+id/regain_verify_code_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@{checkLoginVerifyCodeModel.countdown}"
                    android:textColor="#A4A8B0"
                    android:textSize="13sp"
                    app:layout_constraintLeft_toLeftOf="@id/verify_code_edt"
                    app:layout_constraintTop_toBottomOf="@id/verify_code_edt" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    style="@style/TooCMS.RoundButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="70dp"
                    android:layout_marginRight="45dp"
                    android:layout_marginBottom="45dp"
                    android:text="@string/str_login"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/regain_verify_code_tv"
                    app:onClickCommand="@{checkLoginVerifyCodeModel.onLoginClickBindingCommand}"
                    app:qmui_radius="25dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    onClickCommand="@{checkLoginVerifyCodeModel.onBackClickBindingCommand}"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_default_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>