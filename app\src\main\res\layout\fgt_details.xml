<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="com.toocms.wago.config.Constants" />

        <variable
            name="detailsModel"
            type="com.toocms.wago.ui.details.DetailsModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refresh_srl"
            isVisible="@{!detailsModel.detailType.equals(Constants.DETAIL_TYPE_PRODUCT_PLANS)}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/clr_bg"
            app:srlEnableLoadMore="false"
            app:srlEnableRefresh="false">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/empty"
                    layout="@layout/layout_default_empty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="@{detailsModel.items.empty?View.VISIBLE:View.GONE}" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/content_rv"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{detailsModel.itemBinding}"
                    app:items="@{detailsModel.items}"
                    app:layoutManagerFactory="@{LayoutManagers.linear()}" />
            </FrameLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>

        <com.qmuiteam.qmui.widget.webview.QMUIWebView
            android:id="@+id/webview"
            isVisible="@{detailsModel.detailType.equals(Constants.DETAIL_TYPE_PRODUCT_PLANS)}"
            render="@{detailsModel.content}"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>