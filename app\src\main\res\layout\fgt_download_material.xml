<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <import type="android.view.View" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <variable
            name="downloadMaterialModel"
            type="com.toocms.wago.ui.download_material.DownloadMaterialModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/clr_bg"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <TextView
                    android:id="@+id/text0"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/str_download_material"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/search_edt"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/search_edt"
                    onKey="@{downloadMaterialModel.onKey}"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shape_sol_ffffff_str_a4a8b0_cor_10dp"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:hint="@string/str_certificate"
                    android:imeOptions="actionSearch"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:singleLine="true"
                    android:text="@={downloadMaterialModel.queryString}"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_bias="0.702"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0"
                    app:layout_constraintWidth_percent="0.5" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

        <com.qmuiteam.qmui.widget.tab.QMUITabSegment
            android:id="@+id/tab_segment"
            android:layout_width="match_parent"
            android:layout_height="45dp"
            app:layout_constraintTop_toBottomOf="@id/title" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="tab_segment" />

        <FrameLayout
            android:id="@+id/type_fl"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/language_fl"
            app:layout_constraintTop_toBottomOf="@id/barrier0">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableRight="@drawable/slr_arrow_solid_right_or_down"
                android:drawablePadding="5dp"
                android:padding="15dp"
                android:text="@string/str_type"
                android:textSize="12sp" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/language_fl"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintLeft_toRightOf="@id/type_fl"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier0">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableRight="@drawable/slr_arrow_solid_right_or_down"
                android:drawablePadding="5dp"
                android:padding="15dp"
                android:text="@string/str_language"
                android:textSize="12sp" />
        </FrameLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/clr_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier0" />

        <View
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/clr_bg"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/barrier1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/barrier0" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/clr_bg"
            app:layout_constraintBottom_toBottomOf="@id/barrier1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/barrier1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="type_fl,language_fl" />

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/barrier1"
            app:onLoadMoreCommand="@{downloadMaterialModel.onLoadMoreCommand}"
            app:onRefreshCommand="@{downloadMaterialModel.onRefreshCommand}">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/empty"
                    layout="@layout/layout_default_empty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="@{downloadMaterialModel.items.empty?View.VISIBLE:View.GONE}" />


                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:clipToPadding="false"
                    android:paddingLeft="10dp"
                    android:paddingTop="5dp"
                    android:paddingRight="10dp"
                    android:paddingBottom="5dp"
                    app:itemBinding="@{downloadMaterialModel.itemBinding}"
                    app:items="@{downloadMaterialModel.items}"
                    app:layoutManagerFactory="@{LayoutManagers.linear()}" />
            </FrameLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>