<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="leaveWordModel"
            type="com.toocms.wago.ui.mine.leave_word.LeaveWordModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <TextView
            android:id="@+id/text0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="60dp"
            android:layout_marginTop="50dp"
            android:text="@string/str_input_leave_word"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <EditText
            android:id="@+id/name_edt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:hint="@string/str_name"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:text="@={leaveWordModel.name}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text0" />

        <EditText
            android:id="@+id/phone_edt"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:hint="@string/str_phone_code"
            android:inputType="phone"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:text="@={leaveWordModel.phone}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/name_edt" />

        <EditText
            android:id="@+id/word_edt"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="50dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="50dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:gravity="left|top"
            android:paddingLeft="15dp"
            android:paddingTop="10dp"
            android:paddingRight="15dp"
            android:paddingBottom="10dp"
            android:singleLine="true"
            android:text="@={leaveWordModel.content}"
            app:layout_constraintDimensionRatio="H,28:15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/phone_edt" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/TooCMS.RoundButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="45dp"
            android:layout_marginTop="50dp"
            android:layout_marginRight="45dp"
            android:layout_marginBottom="45dp"
            android:text="@string/str_commit"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/word_edt"
            app:layout_constraintVertical_bias="0"
            app:onClickCommand="@{leaveWordModel.submit}"
            app:qmui_radius="25dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>