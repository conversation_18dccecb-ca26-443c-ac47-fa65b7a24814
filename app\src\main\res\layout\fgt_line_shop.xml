<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="lineShopModel"
            type="com.toocms.wago.ui.line_shop.LineShopModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/TooCMS.RoundButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="45dp"
            android:layout_marginTop="50dp"
            android:layout_marginRight="45dp"
            android:layout_marginBottom="45dp"
            android:text="前往查看"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{lineShopModel.click}"
            app:qmui_radius="25dp" />

        <com.qmuiteam.qmui.widget.webview.QMUIWebView
            android:id="@+id/webview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>