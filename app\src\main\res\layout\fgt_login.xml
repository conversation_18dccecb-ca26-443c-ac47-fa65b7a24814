<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="loginModel"
            type="com.toocms.wago.ui.login.LoginModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/white"
            android:scrollbars="none">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/image0"
                    android:layout_width="160dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="90dp"
                    android:scaleType="centerInside"
                    android:src="@mipmap/icon_logo"
                    app:layout_constraintDimensionRatio="8:3"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="60dp"
                    android:text="用户名/手机号"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/image0" />

                <EditText
                    android:id="@+id/user_name_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={loginModel.username}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text0" />

                <TextView
                    android:id="@+id/text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="50dp"
                    android:layout_marginTop="10dp"
                    android:text="@string/str_password"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_name_edt" />

                <EditText
                    android:id="@+id/password_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:inputType="textPassword"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={loginModel.password}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text1" />

                <ImageView
                    android:id="@+id/show_or_hint_iv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="centerInside"
                    android:src="@mipmap/icon_eye"
                    app:layout_constraintBottom_toBottomOf="@id/password_edt"
                    app:layout_constraintDimensionRatio="W,1:1"
                    app:layout_constraintRight_toRightOf="@id/password_edt"
                    app:layout_constraintTop_toTopOf="@id/password_edt" />

                <TextView
                    android:id="@+id/forget_password_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="5dp"
                    android:paddingTop="10dp"
                    android:paddingRight="5dp"
                    android:paddingBottom="10dp"
                    android:text="@string/str_forget_password_hint"
                    app:layout_constraintLeft_toLeftOf="@id/password_edt"
                    app:layout_constraintTop_toBottomOf="@id/password_edt"
                    app:onClickCommand="@{loginModel.onForgetPasswordClickBindingCommand}" />

                <TextView
                    android:id="@+id/verify_login_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingLeft="5dp"
                    android:paddingTop="10dp"
                    android:paddingRight="5dp"
                    android:paddingBottom="10dp"
                    android:text="@string/str_verify_code_login"
                    app:layout_constraintRight_toRightOf="@id/password_edt"
                    app:layout_constraintTop_toBottomOf="@id/password_edt"
                    app:onClickCommand="@{loginModel.onVerifyCodeLoginClickBindingCommand}" />

                <ImageView
                    android:id="@+id/wechat_login_iv"
                    onClickCommand="@{loginModel.onWXClickBindingCommand}"
                    android:layout_width="50dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="80dp"
                    android:scaleType="centerCrop"
                    android:src="@mipmap/icon_wechat"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/password_edt" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    android:id="@+id/login_btn"
                    style="@style/TooCMS.RoundButton"
                    onClickCommand="@{loginModel.onLoginClickBindingCommand}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="40dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginRight="40dp"
                    android:text="@string/str_login"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/wechat_login_iv"
                    app:qmui_radius="25dp" />

                <TextView
                    android:id="@+id/visitor_visit_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="45dp"
                    android:padding="10dp"
                    android:text="@string/str_visitor_visit"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@id/login_btn"
                    app:layout_constraintTop_toBottomOf="@id/login_btn"
                    app:layout_constraintVertical_bias="0"
                    app:onClickCommand="@{loginModel.onTouristsClickBindingCommand}" />

                <TextView
                    android:id="@+id/register_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="45dp"
                    android:padding="10dp"
                    android:text="@string/str_register"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="@id/login_btn"
                    app:layout_constraintTop_toBottomOf="@id/login_btn"
                    app:layout_constraintVertical_bias="0"
                    app:onClickCommand="@{loginModel.onRegisterClickBindingCommand}" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:gravity="center"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent">

            <CheckBox
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:button="@drawable/selector_cbox"
                android:checked="@={loginModel.agreement}" />

            <TextView
                android:id="@+id/login_agreement"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:textColor="#ED353D"
                android:textSize="12sp" />
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>