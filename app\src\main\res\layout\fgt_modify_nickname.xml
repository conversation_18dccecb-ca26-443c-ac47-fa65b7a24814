<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="modifyNicknameViewModel"
            type="com.toocms.wago.ui.mine.settings.modify_nickname.ModifyNicknameViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg"
        android:padding="50dp">

        <EditText
            android:id="@+id/nickname"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="48dp"
            android:background="@color/clr_transparent"
            android:hint="请输入昵称"
            android:text="@={modifyNicknameViewModel.nickname}"
            app:layout_constraintTop_toTopOf="parent"
            tools:layout_editor_absoluteX="50dp" />

        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:layout_marginTop="8dp"
            android:background="#A4A8B0"
            app:layout_constraintTop_toBottomOf="@id/nickname" />

        <TextView
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="上方横线修改自己的昵称 "
            android:textColor="#A4A8B0"
            android:textSize="13sp"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/TooCMS.RoundButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="200dp"
            android:text="保存"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text"
            app:onClickCommand="@{modifyNicknameViewModel.onBindingCommand}"
            app:qmui_radius="25dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>