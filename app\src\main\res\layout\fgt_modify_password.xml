<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="modifyPasswordViewModel"
            type="com.toocms.wago.ui.mine.settings.modify_password.ModifyPasswordViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <TextView
            android:id="@+id/text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="80dp"
            android:paddingStart="30dp"
            android:text="修改登录密码时需验证您的身份信息"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl0"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:background="@color/white"
            app:layout_constraintTop_toBottomOf="@id/text">

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/llc0"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingStart="50dp"
                android:paddingEnd="50dp"
                app:layout_constraintTop_toTopOf="parent">

                <EditText
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@color/clr_transparent"
                    android:hint="请输入手机号"
                    android:inputType="phone"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp"
                    android:text="@={modifyPasswordViewModel.phone}" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:clickable="@{modifyPasswordViewModel.clickable}"
                    android:text="@{modifyPasswordViewModel.countdown}"
                    app:onClickCommand="@{modifyPasswordViewModel.onCodeBindingCommand}" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <View
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:background="#A4A8B0"
                app:layout_constraintTop_toBottomOf="@id/llc0" />

            <EditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@color/clr_transparent"
                android:hint="请输入验证码"
                android:inputType="number"
                android:paddingStart="50dp"
                android:paddingTop="10dp"
                android:paddingEnd="50dp"
                android:paddingBottom="10dp"
                android:text="@={modifyPasswordViewModel.code}"
                app:layout_constraintTop_toBottomOf="@id/llc0" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/text1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:paddingStart="30dp"
            android:text="密码由至少6位数字和英文字母组成"
            app:layout_constraintTop_toBottomOf="@id/cl0" />

        <EditText
            android:id="@+id/password"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="13dp"
            android:layout_weight="1"
            android:background="@color/white"
            android:hint="请输入密码"
            android:paddingStart="50dp"
            android:paddingTop="10dp"
            android:paddingEnd="50dp"
            android:paddingBottom="10dp"
            android:password="true"
            android:text="@={modifyPasswordViewModel.password}"
            app:layout_constraintTop_toBottomOf="@id/text1" />

        <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
            style="@style/TooCMS.RoundButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_margin="50dp"
            android:layout_marginTop="48dp"
            android:text="提交修改"
            app:layout_constraintHorizontal_bias="0.494"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/password"
            app:onClickCommand="@{modifyPasswordViewModel.onSubmitBindingCommand}"
            app:qmui_radius="25dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>