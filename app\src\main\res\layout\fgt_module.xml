<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="com.blankj.utilcode.util.ConvertUtils" />

        <import type="android.view.View" />

        <variable
            name="moduleModel"
            type="com.toocms.wago.ui.module.ModuleModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/clr_bg"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    android:id="@+id/back_iv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_default_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{moduleModel.onBackClickBindingCommand}" />


                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:text="@{moduleModel.categoryName}"
                    android:textSize="13sp"
                    app:layout_constrainedWidth="true"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@id/back_iv"
                    app:layout_constraintRight_toLeftOf="@id/search_edt"
                    app:layout_constraintTop_toTopOf="parent" />

                <EditText
                    android:id="@+id/search_edt"
                    onKey="@{moduleModel.onKey}"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shape_sol_ffffff_str_a4a8b0_cor_10dp"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:hint="@string/str_product"
                    android:imeOptions="actionSearch"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:singleLine="true"
                    android:text="@={moduleModel.query}"
                    android:textSize="13sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHorizontal_bias="0.75"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.5" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

            <com.google.android.material.appbar.AppBarLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:id="@+id/head_fl"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/shape_sol_clr_bg_bottom_25dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title"
                    app:layout_scrollFlags="scroll|exitUntilCollapsed" />
            </com.google.android.material.appbar.AppBarLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:layout_behavior="@string/appbar_scrolling_view_behavior">

                <androidx.cardview.widget.CardView
                    android:id="@+id/classify_cv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="10dp"
                    android:background="@color/clr_bg"
                    app:cardBackgroundColor="@color/clr_bg"
                    app:cardCornerRadius="10dp"
                    app:cardElevation="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.2">

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/classify_rv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:itemBinding="@{moduleModel.classifyItemBinding}"
                        app:items="@{moduleModel.classifyItems}"
                        app:layoutManagerFactory="@{LayoutManagers.linear()}" />
                </androidx.cardview.widget.CardView>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/filter_cl"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginRight="5dp"
                    android:background="@drawable/shape_sol_clr_bg_cor_10dp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toRightOf="@id/classify_cv"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/type_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/slr_arrow_solid_right_or_down"
                        android:drawablePadding="5dp"
                        android:padding="10dp"
                        android:text="@string/str_product_type"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@id/function_tv"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/function_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/slr_arrow_solid_right_or_down"
                        android:drawablePadding="5dp"
                        android:padding="10dp"
                        android:text="@string/str_product_function"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/type_tv"
                        app:layout_constraintRight_toLeftOf="@id/append_function_tv"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/append_function_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:drawableRight="@drawable/slr_arrow_solid_right_or_down"
                        android:drawablePadding="5dp"
                        android:padding="10dp"
                        android:text="@string/str_append_function"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@id/function_tv"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.scwang.smart.refresh.layout.SmartRefreshLayout
                    android:id="@+id/refresh"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@id/classify_cv"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/filter_cl"
                    app:onRefreshCommand="@{moduleModel.onRefreshCommand}"
                    app:srlEnableLoadMore="false">

                    <FrameLayout
                        android:layout_width="match_parent"
                        android:layout_height="match_parent">

                        <include
                            android:id="@+id/empty"
                            layout="@layout/layout_default_empty"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:visibility="@{moduleModel.contentItems.empty?View.VISIBLE:View.GONE}" />

                        <androidx.recyclerview.widget.RecyclerView
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:clipToPadding="false"
                            android:paddingTop="5dp"
                            android:paddingBottom="5dp"
                            app:itemBinding="@{moduleModel.contentItemBinding}"
                            app:items="@{moduleModel.contentItems}"
                            app:layoutManagerFactory="@{LayoutManagers.linear()}" />
                    </FrameLayout>
                </com.scwang.smart.refresh.layout.SmartRefreshLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>