<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.blankj.utilcode.util.BarUtils" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <variable
            name="newestProductModel"
            type="com.toocms.wago.ui.newest_product.NewestProductModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/clr_bg"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shape_sol_ffffffff_cor_10dp"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:text="@string/str_search"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/empty"
                    layout="@layout/layout_default_empty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="@{newestProductModel.items.empty?View.VISIBLE:View.GONE}" />

                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{newestProductModel.itemBinding}"
                    app:items="@{newestProductModel.items}"
                    app:spanSizeLookup="@{newestProductModel.spanSizeLookup}"
                    app:layoutManagerFactory="@{LayoutManagers.grid(2)}" />
            </FrameLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>