<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <variable
            name="photoViewModel"
            type="com.toocms.wago.ui.photoview.PhotoViewModel" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <com.toocms.wago.ui.photoview.BigPhotoViewPager
            android:id="@+id/photoview_vp_page"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/black" />

        <LinearLayout
            android:id="@+id/photoview_linlay_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="70dp"
            android:orientation="horizontal" />

        <TextView
            android:id="@+id/photoview_tv_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:gravity="center"
            android:paddingBottom="70dp"
            android:layout_alignParentBottom="true"
            android:text="2/10"
            android:textColor="@android:color/white" />
        <!-- <uk.co.senab.photoview.PhotoView
             android:id="@+id/photoview_phv_bitmap"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:background="@color/black" />-->
    </RelativeLayout>
</layout>