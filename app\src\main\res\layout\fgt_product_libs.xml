<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LineManagers" />

        <import type="androidx.recyclerview.widget.LinearLayoutManager" />

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="productLibsModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/clr_bg"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            android:paddingBottom="10dp"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginLeft="15dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="15dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shape_sol_ffffffff_cor_10dp"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:text="@string/str_search"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{productLibsModel.onSearchClickBindingCommand}" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <androidx.cardview.widget.CardView
            android:id="@+id/card_cv"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:background="@color/clr_bg"
            app:cardBackgroundColor="@color/clr_bg"
            app:cardCornerRadius="10dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintWidth_percent="0.2">

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:itemBinding="@{productLibsModel.topClassifyItemBinding}"
                app:items="@{productLibsModel.topClassifyItems}"
                app:layoutManagerFactory="@{LayoutManagers.linear()}" />
        </androidx.cardview.widget.CardView>

        <FrameLayout
            android:id="@+id/content_fl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/card_cv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>