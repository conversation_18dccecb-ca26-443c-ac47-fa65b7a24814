<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LineManagers" />

        <import type="androidx.recyclerview.widget.LinearLayoutManager" />

        <variable
            name="productLibsClassifyModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsClassifyModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <TextView
            android:id="@+id/top_classify_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:text="@{productLibsClassifyModel.classify}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/secondary_classify_rv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:padding="10dp"
            app:itemBinding="@{productLibsClassifyModel.secondaryClassifyItemBinding}"
            app:items="@{productLibsClassifyModel.secondaryClassifyItems}"
            app:layoutManagerFactory="@{LayoutManagers.linear(LinearLayoutManager.HORIZONTAL,false)}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/top_classify_tv"
            app:lineManagerFactory="@{LineManagers.vertical(10,0x00000000)}" />

        <TextView
            android:id="@+id/secondary_classify_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="10dp"
            android:text="@{productLibsClassifyModel.smallClassify}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/secondary_classify_rv" />

        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingLeft="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            app:itemBinding="@{productLibsClassifyModel.smallClassifyItemBinding}"
            app:items="@{productLibsClassifyModel.smallClassifyItems}"
            app:layoutManagerFactory="@{LayoutManagers.grid(2)}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/secondary_classify_tv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>