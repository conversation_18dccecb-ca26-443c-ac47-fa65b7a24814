<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <variable
            name="productLibsNewestModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsNewestModel" />
    </data>

    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/refresh"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        app:onLoadMoreCommand="@{productLibsNewestModel.onLoadMoreCommand}"
        app:onRefreshCommand="@{productLibsNewestModel.onRefreshCommand}">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <include
                android:id="@+id/empty"
                layout="@layout/layout_default_empty"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="@{productLibsNewestModel.items.empty?View.VISIBLE:View.GONE}" />

            <androidx.recyclerview.widget.RecyclerView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:clipToPadding="false"
                android:paddingTop="5dp"
                android:paddingBottom="5dp"
                app:itemBinding="@{productLibsNewestModel.itemBinding}"
                app:items="@{productLibsNewestModel.items}"
                app:layoutManagerFactory="@{LayoutManagers.linear()}" />
        </FrameLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>
</layout>