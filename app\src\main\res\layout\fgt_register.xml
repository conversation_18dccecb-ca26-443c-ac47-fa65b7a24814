<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="registerModel"
            type="com.toocms.wago.ui.login.register.RegisterModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/image0"
                    android:layout_width="70dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="70dp"
                    android:layout_marginRight="50dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/icon_logo"
                    app:layout_constraintDimensionRatio="H,14:5"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="45dp"
                    android:text="@string/str_register"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/image0" />

                <TextView
                    android:id="@+id/text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="55dp"
                    android:text="@string/str_input_user_name_hint"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text0" />

                <EditText
                    android:id="@+id/user_name_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={registerModel.username}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text1" />


                <TextView
                    android:id="@+id/text2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="15dp"
                    android:text="@string/str_input_phone_code_hint"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/user_name_edt" />

                <EditText
                    android:id="@+id/phone_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={registerModel.phone}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text2" />

                <TextView
                    android:id="@+id/text3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="15dp"
                    android:text="@string/str_input_verify_code_hint"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/phone_edt" />

                <EditText
                    android:id="@+id/verify_code_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:inputType="number"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={registerModel.code}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text3" />

                <TextView
                    onClickCommand="@{registerModel.onCodeBindingCommand}"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:clickable="@{registerModel.clickable}"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@{registerModel.countdown}"
                    android:textColor="@drawable/clr_acquire_verify_code"
                    app:layout_constraintBottom_toBottomOf="@id/verify_code_edt"
                    app:layout_constraintRight_toRightOf="@id/verify_code_edt"
                    app:layout_constraintTop_toTopOf="@id/verify_code_edt" />


                <TextView
                    android:id="@+id/text4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="15dp"
                    android:text="请输入密码"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/verify_code_edt" />

                <EditText
                    android:id="@+id/newest_password_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:inputType="textPassword"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={registerModel.password}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text4" />

                <TextView
                    android:id="@+id/text5"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="5dp"
                    android:text="@string/str_password_limit_hint"
                    android:textColor="#A4A8B0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/newest_password_edt" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    style="@style/TooCMS.RoundButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginRight="45dp"
                    android:layout_marginBottom="45dp"
                    android:text="@string/str_register"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text5"
                    app:onClickCommand="@{registerModel.onRegisterBindingCommand}"
                    app:qmui_radius="25dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_default_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{registerModel.onBackClickBindingCommand}" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>