<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.toocms.tab.binding.viewadapter.recyclerview.LayoutManagers" />

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="searchResultModel"
            type="com.toocms.wago.ui.search.SearchResultModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/title_toolbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/clr_bg"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    android:id="@+id/back_iv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_default_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{searchResultModel.onBackClickBindingCommand}" />

                <EditText
                    android:id="@+id/input_edt"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginBottom="5dp"
                    android:background="@drawable/shape_sol_ffffffff_cor_10dp"
                    android:drawableLeft="@mipmap/icon_search"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:hint="@string/str_search_content_empty_hint"
                    android:paddingLeft="15dp"
                    android:paddingRight="15dp"
                    android:singleLine="true"
                    android:text="@={searchResultModel.keyword}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toRightOf="@id/back_iv"
                    app:layout_constraintRight_toLeftOf="@id/search_tv"
                    app:layout_constraintTop_toTopOf="parent" />


                <TextView
                    android:id="@+id/search_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:gravity="center"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/str_search_chinese"
                    android:textColor="@color/clr_main"
                    android:textSize="@dimen/action_menu_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{searchResultModel.onSearchClickBindingCommand}" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <com.scwang.smart.refresh.layout.SmartRefreshLayout
            android:id="@+id/refresh"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_toolbar"
            app:onLoadMoreCommand="@{searchResultModel.onLoadMoreCommand}"
            app:onRefreshCommand="@{searchResultModel.onRefreshCommand}">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <include
                    android:id="@+id/empty"
                    layout="@layout/layout_default_empty"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:visibility="@{searchResultModel.items.empty?View.VISIBLE:View.GONE}" />

                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:itemBinding="@{searchResultModel.itemBinding}"
                    app:items="@{searchResultModel.items}"
                    app:layoutManagerFactory="@{LayoutManagers.linear()}" />
            </FrameLayout>
        </com.scwang.smart.refresh.layout.SmartRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>