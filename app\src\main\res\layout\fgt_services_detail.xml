<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="servicesDetailViewModel"
            type="com.toocms.wago.ui.about_us.services.detail.ServicesDetailViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="20dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:padding="25dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="@{servicesDetailViewModel.title}"
                android:textSize="17sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/text1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:text="@{servicesDetailViewModel.fun}"
                android:textColor="#A5A8B1"
                android:textSize="13sp"
                app:layout_constraintTop_toBottomOf="@id/text" />

            <TextView
                android:id="@+id/text2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:drawablePadding="15dp"
                android:text="@{servicesDetailViewModel.telephone}"
                android:textSize="13sp"
                app:drawableLeftCompat="@mipmap/icon_flag_phone"
                app:layout_constraintTop_toBottomOf="@id/text1" />

            <TextView
                android:id="@+id/text3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:drawablePadding="15dp"
                android:text="@{servicesDetailViewModel.email}"
                android:textSize="13sp"
                app:drawableLeftCompat="@mipmap/icon_flag_email"
                app:layout_constraintTop_toBottomOf="@id/text2" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                style="@style/TooCMS.RoundButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="50dp"
                android:text="立即拨打"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text3"
                app:onClickCommand="@{servicesDetailViewModel.call}"
                app:qmui_radius="25dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>