<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="settingsViewModel"
            type="com.toocms.wago.ui.mine.settings.SettingsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl0"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="25dp"
            android:background="@drawable/shape_sol_clr_main_cor_10dp"
            android:padding="20dp"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/text1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="当前版本"
                android:textColor="@color/white"
                android:textSize="25sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/text2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@{settingsViewModel.version}"
                android:textColor="@color/white"
                android:textSize="18sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text1" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:paddingStart="20dp"
                android:paddingTop="5dp"
                android:paddingEnd="20dp"
                android:paddingBottom="5dp"
                android:text="立即更新"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/text2"
                app:onClickCommand="@{settingsViewModel.update}"
                app:qmui_borderColor="@color/white" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llc0"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="40dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:elevation="3dp"
            android:orientation="vertical"
            app:divider="@drawable/shape_sol_clr_bg_height_1dp"
            app:dividerPadding="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/cl0"
            app:showDividers="middle">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:onClickCommand="@{settingsViewModel.onModifyNicknameBindingCommand}">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:padding="15dp"
                    android:text="修改昵称"
                    app:drawableLeftCompat="@mipmap/icon_modify_nickname" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:padding="15dp"
                    android:text="这里是昵称鸭"
                    android:textColor="#A5A8B1"
                    app:drawableRightCompat="@mipmap/icon_arrow_right" />
            </LinearLayout>

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="更改手机号"
                app:drawableLeftCompat="@mipmap/icon_change_phone"
                app:drawableRightCompat="@mipmap/icon_arrow_right"
                app:onClickCommand="@{settingsViewModel.onChangePhoneBindingCommand}" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:elevation="3dp"
            android:orientation="vertical"
            app:divider="@drawable/shape_sol_clr_bg_height_1dp"
            app:dividerPadding="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llc0"
            app:showDividers="middle">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="修改密码"
                app:drawableLeftCompat="@mipmap/icon_modify_password"
                app:drawableRightCompat="@mipmap/icon_arrow_right"
                app:onClickCommand="@{settingsViewModel.onModifyPasswordBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="用户协议"
                app:drawableLeftCompat="@mipmap/icon_modify_nickname"
                app:drawableRightCompat="@mipmap/icon_arrow_right"
                app:onClickCommand="@{settingsViewModel.policyBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="隐私政策"
                app:drawableLeftCompat="@mipmap/icon_modify_password"
                app:drawableRightCompat="@mipmap/icon_arrow_right"
                app:onClickCommand="@{settingsViewModel.agreementBindingCommand}" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:onClickCommand="@{settingsViewModel.onClearCacheBindingCommand}">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:padding="15dp"
                    android:text="清除缓存"
                    app:drawableLeftCompat="@mipmap/icon_clear_cache" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:drawablePadding="10dp"
                    android:gravity="center_vertical"
                    android:padding="15dp"
                    android:text="@{settingsViewModel.size}"
                    android:textColor="#A5A8B1" />
            </LinearLayout>
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>