<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="userCenterModel"
            type="com.toocms.wago.ui.mine.UserCenterModel" />

        <import type="com.toocms.wago.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/clr_bg">

        <ImageView
            android:id="@+id/head_bg_iv"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@mipmap/icon_user_center_head_bg"
            app:layout_constraintDimensionRatio="H,25:18"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_back_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="W,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="@string/str_personal_center"
                    android:textColor="@color/white"
                    android:textSize="@dimen/action_title_size"
                    app:onClickCommand="@{userCenterModel.onBackClickBindingCommand}" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>

        <com.qmuiteam.qmui.widget.QMUIRadiusImageView
            android:id="@+id/user_head_riv"
            placeholderRes="@{R.mipmap.img_default}"
            url="@{userCenterModel.head}"
            android:layout_width="65dp"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="@id/head_bg_iv"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintHorizontal_bias="0.15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/head_bg_iv"
            app:onClickCommand="@{userCenterModel.onUploadHeadFrameClickBindingCommand}"
            app:qmui_border_color="@color/clr_main"
            app:qmui_border_width="1dp"
            app:qmui_is_circle="true" />

        <TextView
            android:id="@+id/nickname_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:text="@{userCenterModel.nickname}"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="@id/user_head_riv"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toRightOf="@id/user_head_riv"
            app:layout_constraintRight_toLeftOf="@id/setting_iv"
            app:layout_constraintTop_toTopOf="@id/user_head_riv" />

        <ImageView
            android:id="@+id/setting_iv"
            android:layout_width="35dp"
            android:layout_height="0dp"
            android:scaleType="centerInside"
            android:src="@drawable/icon_setting"
            app:layout_constraintBottom_toBottomOf="@id/user_head_riv"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintHorizontal_bias="0.9"
            app:layout_constraintRight_toLeftOf="@id/message_iv"
            app:layout_constraintTop_toTopOf="@id/user_head_riv"
            app:onClickCommand="@{userCenterModel.onSettingsClickBindingCommand}" />

        <ImageView
            android:id="@+id/message_iv"
            android:layout_width="35dp"
            android:layout_height="0dp"
            android:padding="5dp"
            android:scaleType="centerInside"
            android:src="@mipmap/icon_message"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/user_head_riv"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintHorizontal_bias="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/user_head_riv"
            app:onClickCommand="@{userCenterModel.startLeaveWord}" />

        <View
            android:layout_width="8dp"
            android:layout_height="0dp"
            android:background="@drawable/shape_sol_ff0000_cor_5dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/message_iv"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintHorizontal_bias="0.8"
            app:layout_constraintLeft_toLeftOf="@id/message_iv"
            app:layout_constraintRight_toRightOf="@id/message_iv"
            app:layout_constraintTop_toTopOf="@id/message_iv"
            app:layout_constraintVertical_bias="0.2" />

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llc0"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:elevation="3dp"
            android:orientation="vertical"
            app:divider="@drawable/shape_sol_clr_bg_height_1dp"
            app:dividerPadding="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/head_bg_iv"
            app:showDividers="middle">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_my_share"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="@string/str_my_share"
                app:onClickCommand="@{userCenterModel.onMyShareClickBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_my_collect"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="@string/str_my_collect"
                app:onClickCommand="@{userCenterModel.onMyCollectClickBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_browse_record"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="@string/str_browse_record"
                app:onClickCommand="@{userCenterModel.onBrowseRecordClickBindingCommand}" />

        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.appcompat.widget.LinearLayoutCompat
            android:id="@+id/llc1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:elevation="3dp"
            android:orientation="vertical"
            app:divider="@drawable/shape_sol_clr_bg_height_1dp"
            app:dividerPadding="15dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/llc0"
            app:showDividers="middle">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_download_record"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="@string/str_download_record"
                app:onClickCommand="@{userCenterModel.onDownloadRecordClickBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_system_message"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="@string/str_system_message"
                app:onClickCommand="@{userCenterModel.onSystemMessageClickBindingCommand}" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:drawableLeft="@mipmap/icon_system_leave"
                android:drawableRight="@mipmap/icon_arrow_more"
                android:drawablePadding="10dp"
                android:gravity="center_vertical"
                android:padding="15dp"
                android:text="意见留言"
                app:onClickCommand="@{userCenterModel.startLeaveWord}" />
        </androidx.appcompat.widget.LinearLayoutCompat>

        <androidx.constraintlayout.widget.ConstraintLayout
            isVisible="@{userCenterModel.isShowHeadFrame}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#59000000"
            android:elevation="3dp"
            app:onClickCommand="@{userCenterModel.onUploadHeadFrameClickBindingCommand}">

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                android:id="@+id/head"
                placeholderRes="@{R.mipmap.img_default}"
                url="@{userCenterModel.head}"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginBottom="100dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_is_circle="true" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                style="@style/TooCMS.RoundButton"
                android:layout_width="120dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="30dp"
                android:text="上传头像"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/head"
                app:onClickCommand="@{userCenterModel.onUploadHeadClickBindingCommand}"
                app:qmui_radius="25dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>