<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.BarUtils" />

        <variable
            name="verifyCodeLoginModel"
            type="com.toocms.wago.ui.login.verify_code_login.VerifyCodeLoginModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/image0"
                    android:layout_width="70dp"
                    android:layout_height="0dp"
                    android:layout_marginTop="70dp"
                    android:layout_marginRight="50dp"
                    android:scaleType="fitXY"
                    android:src="@mipmap/icon_logo"
                    app:layout_constraintDimensionRatio="H,14:5"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/text0"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="55dp"
                    android:layout_marginTop="45dp"
                    android:text="@string/str_verify_code_login"
                    android:textSize="25sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/image0" />

                <TextView
                    android:id="@+id/text1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_margin="55dp"
                    android:text="@string/str_input_phone_code_hint"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text0" />

                <EditText
                    android:id="@+id/phone_edt"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginRight="45dp"
                    android:background="@drawable/shape_sol_00000000_str_eef0f2_cor_5dp"
                    android:inputType="phone"
                    android:maxLength="11"
                    android:padding="10dp"
                    android:singleLine="true"
                    android:text="@={verifyCodeLoginModel.phone}"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/text1" />

                <ImageView
                    android:id="@+id/clear_iv"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_delete"
                    app:layout_constraintBottom_toBottomOf="@id/phone_edt"
                    app:layout_constraintDimensionRatio="W,1:1"
                    app:layout_constraintRight_toRightOf="@id/phone_edt"
                    app:layout_constraintTop_toTopOf="@id/phone_edt" />

                <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                    style="@style/TooCMS.RoundButton"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="45dp"
                    android:layout_marginTop="50dp"
                    android:layout_marginRight="45dp"
                    android:layout_marginBottom="45dp"
                    android:text="@string/str_acquire_verify_code"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/phone_edt"
                    app:onClickCommand="@{verifyCodeLoginModel.onAcquireVerifyCodeClickBindingCommand}"
                    app:qmui_radius="25dp" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

        <androidx.appcompat.widget.Toolbar
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@{BarUtils.getStatusBarHeight()}"
            app:contentInsetEnd="0dp"
            app:contentInsetStart="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/action_height_size">

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleType="center"
                    android:src="@mipmap/icon_default_back"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="H,1:1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:onClickCommand="@{verifyCodeLoginModel.onBackClickBindingCommand}" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.appcompat.widget.Toolbar>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>