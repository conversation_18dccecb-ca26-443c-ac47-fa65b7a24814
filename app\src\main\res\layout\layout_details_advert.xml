<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="detailsAdvertItemModel"
            type="com.toocms.wago.ui.details.DetailsAdvertItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp">

        <com.toocms.wago.widget.VideoBanner
            android:id="@+id/advert_sib"
            onItemClickListener="@{detailsAdvertItemModel.onItemClickListener}"
            android:layout_width="match_parent"
            android:layout_height="175dp"
            android:background="@color/white"
            android:elevation="3dp"
            android:padding="5dp"
            app:bb_indicatorSelectColor="@color/clr_main"
            app:bb_indicatorUnselectColor="@color/clr_bg"
            app:items="@{detailsAdvertItemModel.bannerItems}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/download_fl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="5dp"
            android:background="@drawable/shape_sol_clr_main_cor_5dp"
            android:elevation="4dp"
            android:minWidth="60dp"
            android:padding="5dp"
            app:layout_constraintBottom_toTopOf="@id/collect_fl"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:onClickCommand="@{detailsAdvertItemModel.download}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawablePadding="5dp"
                android:text="@string/str_download"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:drawableLeftCompat="@mipmap/icon_download_white" />
        </FrameLayout>

        <FrameLayout
            android:id="@+id/collect_fl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginRight="5dp"
            android:background="@drawable/shape_sol_clr_main_cor_5dp"
            android:elevation="4dp"
            android:minWidth="60dp"
            android:padding="5dp"
            app:layout_constraintBottom_toTopOf="@id/transpond_fl"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/download_fl"
            app:onClickCommand="@{detailsAdvertItemModel.collect}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@mipmap/icon_collect_white"
                android:drawablePadding="5dp"
                android:text="@string/str_collect"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </FrameLayout>

        <FrameLayout
            android:id="@+id/transpond_fl"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginTop="10dp"
            android:layout_marginRight="5dp"
            android:background="@drawable/shape_sol_clr_main_cor_5dp"
            android:elevation="4dp"
            android:minWidth="60dp"
            android:padding="5dp"
            app:layout_constraintBottom_toBottomOf="@id/advert_sib"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/collect_fl"
            app:onClickCommand="@{detailsAdvertItemModel.share}">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:drawableLeft="@mipmap/icon_transpond_white"
                android:drawablePadding="5dp"
                android:text="@string/str_transpond"
                android:textColor="@color/white"
                android:textSize="12sp" />

        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>