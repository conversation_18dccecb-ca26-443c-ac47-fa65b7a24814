<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="com.toocms.wago.recycer.layout_manager.LayoutManagerExtend"/>

        <variable
            name="detailsIntroModel"
            type="com.toocms.wago.ui.details.DetailsIntroItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/content_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_sol_ffffffff_cor_10dp"
            android:paddingTop="30dp"
            android:paddingBottom="10dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            android:elevation="3dp"
            app:items="@{detailsIntroModel.items}"
            app:itemBinding="@{detailsIntroModel.itemBinding}"
            app:layoutManagerFactory="@{LayoutManagerExtend.linerVerticalCannotScroll()}"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="5dp"
            android:minWidth="60dp"
            android:background="@drawable/shape_sol_clr_main_cor_10dp"
            android:textSize="13sp"
            android:gravity="center"
            android:textColor="@color/white"
            android:text="@string/str_preview"
            android:elevation="3dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/content_rv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintBottom_toTopOf="@id/content_rv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>