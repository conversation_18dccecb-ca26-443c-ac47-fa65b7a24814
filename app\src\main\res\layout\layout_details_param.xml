<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.toocms.wago.recycer.layout_manager.LayoutManagerExtend" />

        <variable
            name="detailsParamItemModel"
            type="com.toocms.wago.ui.details.DetailsParamItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/shape_sol_ffffffff_cor_10dp"
        android:elevation="3dp">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/slr_addition_or_minus"
            android:drawablePadding="10dp"
            android:padding="15dp"
            android:text="@{detailsParamItemModel.name}"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{detailsParamItemModel.onTitleClickBindingCommand}"
            app:viewSelectStatus="@{detailsParamItemModel.isSelected}" />

        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="15dp"
            android:visibility="@{detailsParamItemModel.isSelected?View.VISIBLE:View.GONE}"
            app:itemBinding="@{detailsParamItemModel.itemBinding}"
            app:items="@{detailsParamItemModel.items}"
            app:layoutManagerFactory="@{LayoutManagerExtend.linerVerticalCannotScroll()}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>