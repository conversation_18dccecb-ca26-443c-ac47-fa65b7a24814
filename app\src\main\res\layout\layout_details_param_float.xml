<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="detailsParamFloatItemModel"
            type="com.toocms.wago.ui.details.DetailsParamFloatItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        android:background="@drawable/shape_sol_ffffffff_cor_10dp"
        android:elevation="3dp">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/slr_addition_or_minus"
            android:drawablePadding="10dp"
            android:padding="15dp"
            android:text="XXXXXX"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{detailsParamFloatItemModel.onTitleClickBindingCommand}"
            app:viewSelectStatus="@{detailsParamFloatItemModel.isSelected}" />

        <com.qmuiteam.qmui.widget.QMUIFloatLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="15dp"
            android:visibility="@{detailsParamFloatItemModel.isSelected?View.VISIBLE:View.GONE}"
            app:floatChildren="@{detailsParamFloatItemModel.params}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv"
            app:qmui_childHorizontalSpacing="10dp"
            app:qmui_childVerticalSpacing="10dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>