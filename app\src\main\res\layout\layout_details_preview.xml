<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.toocms.wago.recycer.layout_manager.LayoutManagerExtend" />

        <variable
            name="detailsPreviewItemModel"
            type="com.toocms.wago.ui.details.DetailsPreviewItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.cardview.widget.CardView
            android:id="@+id/card_cv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="25dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            android:background="@color/white"
            android:clipChildren="false"
            app:cardCornerRadius="10dp"
            app:cardElevation="3dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipChildren="false">

                <androidx.recyclerview.widget.RecyclerView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="13dp"
                    android:paddingTop="23dp"
                    android:paddingRight="13dp"
                    android:paddingBottom="13dp"
                    app:itemBinding="@{detailsPreviewItemModel.itemBinding}"
                    app:items="@{detailsPreviewItemModel.items}"
                    app:layoutManagerFactory="@{LayoutManagerExtend.gridVerticalCannotScroll(3)}"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_sol_clr_main_cor_10dp"
            android:elevation="3dp"
            android:gravity="center"
            android:minWidth="60dp"
            android:padding="5dp"
            android:text="@string/str_preview"
            android:textColor="@color/white"
            android:textSize="13sp"
            app:layout_constraintBottom_toTopOf="@id/card_cv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/card_cv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>