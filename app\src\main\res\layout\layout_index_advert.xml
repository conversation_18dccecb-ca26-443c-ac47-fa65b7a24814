<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="indexAdvertModel"
            type="com.toocms.wago.ui.index.IndexAdvertModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/clr_bg"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="5dp"
            android:layout_marginRight="15dp"
            android:layout_marginBottom="5dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="H,35:17"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.toocms.wago.widget.RadiusImageBanner
                items="@{indexAdvertModel.bannerItems}"
                onItemClickListener="@{indexAdvertModel.onItemClickListener}"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:bb_indicatorSelectColor="#2F2F2F"
                app:bb_indicatorUnselectColor="#DDDDDD" />
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>