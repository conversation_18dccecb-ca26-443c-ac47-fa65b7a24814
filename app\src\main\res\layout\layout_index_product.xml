<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.ConvertUtils" />

        <variable
            name="indexProductItemModel"
            type="com.toocms.wago.ui.index.IndexProductItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="@{0==indexProductItemModel.position%2?ConvertUtils.dp2px(15):ConvertUtils.dp2px(5)}"
        android:paddingTop="5dp"
        android:paddingRight="@{0==indexProductItemModel.position%2?ConvertUtils.dp2px(5):ConvertUtils.dp2px(15)}"
        android:paddingBottom="5dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/shape_sol_clr_bg_cor_10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{indexProductItemModel.onItemClickBindingCommand}">

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                android:id="@+id/cover_iv"
                url="@{indexProductItemModel.url}"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:qmui_border_color="@color/clr_main"
                app:qmui_border_width="1dp"
                app:qmui_corner_radius="10dp" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginBottom="10dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{indexProductItemModel.product}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="@id/cover_iv"
                app:layout_constraintRight_toLeftOf="@id/arrow_tv"
                app:layout_constraintTop_toBottomOf="@id/cover_iv" />

            <ImageView
                android:id="@+id/arrow_tv"
                android:layout_width="25dp"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:scaleType="centerInside"
                android:src="@mipmap/icon_arrow_more"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintRight_toRightOf="@id/cover_iv"
                app:layout_constraintTop_toBottomOf="@id/cover_iv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>