<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="indexUserItemModel"
            type="com.toocms.wago.ui.index.IndexUserItemModel" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/clr_bg">

        <TextView
            android:id="@+id/text0"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:text="欢迎进入\nWAGO Product Catalogue"
            android:textColor="@color/clr_main"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed" />

        <TextView
            android:id="@id/text1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="30dp"
            android:text="@string/str_search_hint"
            android:textColor="#A4A8B0"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/text0" />

        <com.qmuiteam.qmui.widget.QMUIRadiusImageView
            android:id="@+id/user_head_riv"
            url="@{indexUserItemModel.head}"
            android:layout_width="60dp"
            android:layout_height="0dp"
            android:layout_marginTop="20dp"
            android:layout_marginRight="20dp"
            android:scaleType="centerCrop"
            android:src="@mipmap/img_default"
            app:layout_constraintBottom_toTopOf="@id/text2"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_chainStyle="packed"
            app:onClickCommand="@{indexUserItemModel.onUserCenterClickBindingCommand}"
            app:qmui_border_width="0dp"
            app:qmui_is_circle="true" />

        <TextView
            android:id="@+id/text2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="20dp"
            android:text="@string/str_personal_center"
            android:textColor="@color/clr_main"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/user_head_riv"
            app:layout_constraintRight_toRightOf="@id/user_head_riv"
            app:layout_constraintTop_toBottomOf="@id/user_head_riv"
            app:onClickCommand="@{indexUserItemModel.onUserCenterClickBindingCommand}" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>