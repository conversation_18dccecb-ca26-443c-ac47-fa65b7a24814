<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="searchFloatModel"
            type="com.toocms.wago.ui.search.SearchFloatModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.qmuiteam.qmui.widget.QMUIFloatLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="15dp"
            app:floatChildren="@{searchFloatModel.items}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:qmui_childHorizontalSpacing="10dp"
            app:qmui_childVerticalSpacing="10dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>