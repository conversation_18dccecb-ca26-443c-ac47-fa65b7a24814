<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <variable
            name="searchTitleModel"
            type="com.toocms.wago.ui.search.SearchTitleModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:maxLines="1"
            android:padding="15dp"
            android:text="@{searchTitleModel.title}"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/clear_tv"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/clear_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:drawableLeft="@mipmap/icon_trash_can"
            android:drawablePadding="5dp"
            android:padding="15dp"
            android:text="@string/str_clear_record"
            android:visibility="@{searchTitleModel.isShowClear?View.VISIBLE:View.GONE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{searchTitleModel.onClearBindingCommand}" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>