<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="android.view.View" />

        <import type="com.toocms.wago.recycer.layout_manager.LayoutManagerExtend" />

        <variable
            name="detailsParamItemItemModel2"
            type="com.toocms.wago.ui.details.DetailsParamItemItemModel2" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="15dp"
        android:layout_marginBottom="5dp"
        android:elevation="3dp">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:drawableRight="@drawable/slr_addition_or_minus"
            android:drawablePadding="10dp"
            android:padding="15dp"
            android:text="@{detailsParamItemItemModel2.specName}"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{detailsParamItemItemModel2.onTitleClickBindingCommand}"
            app:viewSelectStatus="@{detailsParamItemItemModel2.isSelected}" />

        <androidx.recyclerview.widget.RecyclerView
            isVisible="@{detailsParamItemItemModel2.isSelected}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingBottom="15dp"
            app:itemBinding="@{detailsParamItemItemModel2.itemBinding}"
            app:items="@{detailsParamItemItemModel2.items}"
            app:layoutManagerFactory="@{LayoutManagerExtend.linerVerticalCannotScroll()}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_tv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>