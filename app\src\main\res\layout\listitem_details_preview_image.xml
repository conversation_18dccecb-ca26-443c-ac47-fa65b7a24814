<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="detailsPreviewImageItemModel"
            type="com.toocms.wago.ui.details.DetailsPreviewImageItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="2dp">

        <com.qmuiteam.qmui.widget.QMUIRadiusImageView
            url="@{detailsPreviewImageItemModel.url}"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:scaleType="centerCrop"
            android:src="@mipmap/img_default"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="H,5:7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:onClickCommand="@{detailsPreviewImageItemModel.onItemClickBindingCommand}"
            app:qmui_border_color="@color/clr_main"
            app:qmui_border_width="1dp"
            app:qmui_corner_radius="1dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>