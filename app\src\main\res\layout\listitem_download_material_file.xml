<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="downloadMaterialFileItemModel"
            type="com.toocms.wago.ui.download_material.DownloadMaterialFileItemModel" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="5dp"
        android:background="@color/clr_bg"
        app:cardBackgroundColor="@color/clr_bg"
        app:cardCornerRadius="5dp"
        app:cardElevation="0dp"
        app:onClickCommand="@{downloadMaterialFileItemModel.onItemClickBindingCommand}">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                android:id="@+id/cover_iv"
                url="@{downloadMaterialFileItemModel.imgUrl}"
                android:layout_width="80dp"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0"
                app:qmui_border_width="0dp"
                app:qmui_corner_radius="5dp" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="@{downloadMaterialFileItemModel.title}"
                android:textColor="@color/clr_main"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/intro_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:text="@{downloadMaterialFileItemModel.subTitle}"
                android:textSize="12sp"
                app:layout_constraintBottom_toTopOf="@id/type_tv"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv"
                app:layout_constraintVertical_bias="0.85"
                app:layout_constraintVertical_chainStyle="packed" />

            <TextView
                android:id="@+id/type_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:text="@{downloadMaterialFileItemModel.fileName}"
                android:textColor="@color/clr_main"
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toLeftOf="@id/remove_iv"
                app:layout_constraintTop_toBottomOf="@id/intro_tv" />

            <ImageView
                android:id="@+id/remove_iv"
                android:layout_width="15dp"
                android:layout_height="0dp"
                android:layout_margin="10dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/icon_arrow_add_annulus"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintRight_toRightOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>