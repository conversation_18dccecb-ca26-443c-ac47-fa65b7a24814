<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.blankj.utilcode.util.StringUtils" />

        <variable
            name="moduleContentItemModel"
            type="com.toocms.wago.ui.module.ModuleContentItemModel" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        android:background="@color/clr_bg"
        android:foreground="@drawable/shape_sol_eff0f1_str_eff0f1_cor_10dp"
        app:cardBackgroundColor="@color/clr_bg"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="8dp"
            app:onClickCommand="@{moduleContentItemModel.detail}">

            <View
                android:id="@+id/view0"
                android:layout_width="5dp"
                android:layout_height="0dp"
                android:layout_marginLeft="5dp"
                android:background="@drawable/shape_sol_clr_main_cor_10dp"
                app:layout_constraintBottom_toBottomOf="@id/name_tv"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/name_tv" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text='@{moduleContentItemModel.title}'
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/view0"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.qmuiteam.qmui.widget.QMUIRadiusImageView
                url="@{moduleContentItemModel.url}"
                android:layout_width="match_parent"
                android:layout_height="100dp"
                android:layout_marginTop="6dp"
                app:layout_constraintTop_toBottomOf="@id/name_tv"
                app:qmui_border_width="0dp"
                app:qmui_corner_radius="10dp" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>