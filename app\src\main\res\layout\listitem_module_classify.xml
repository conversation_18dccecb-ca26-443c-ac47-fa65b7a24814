<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="moduleClassifyItemModel"
            type="com.toocms.wago.ui.module.ModuleClassifyItemModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/slr_shape_clr_bg_or_lay_bg_white_left_clr_main_line"
        app:onClickCommand="@{moduleClassifyItemModel.onClickBindingCommand}"
        app:viewSelectStatus="@{moduleClassifyItemModel.isSelected}">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="10dp"
            android:text="@{moduleClassifyItemModel.classify}"
            android:textColor="@drawable/clr_000000_or_clr_main"
            android:textSize="10sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_min="55dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>