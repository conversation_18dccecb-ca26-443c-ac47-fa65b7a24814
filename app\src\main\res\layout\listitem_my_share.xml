<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="myShareItemModel"
            type="com.toocms.wago.ui.mine.my_share.MyShareItemModel" />

    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        android:foreground="@drawable/shape_sol_00000000_str_clr_main_cor_10dp"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp"
            app:onClickCommand="@{myShareItemModel.startDetail}">

            <ImageView
                android:id="@+id/cover_iv"
                url="@{myShareItemModel.url}"
                android:layout_width="90dp"
                android:layout_height="55dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="H,23:17"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view0"
                android:layout_width="5dp"
                android:layout_height="0dp"
                android:layout_marginLeft="15dp"
                android:background="@drawable/shape_sol_clr_main_cor_10dp"
                app:layout_constraintBottom_toBottomOf="@id/name_tv"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintTop_toTopOf="@id/name_tv" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="15dp"
                android:layout_marginRight="15dp"
                android:ellipsize="end"
                android:maxLines="1"
                android:text="@{myShareItemModel.title}"
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintLeft_toRightOf="@id/view0"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="15dp"
                android:ellipsize="end"
                android:maxLines="2"
                android:text="@{myShareItemModel.subTitle}"
                android:textSize="9sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>