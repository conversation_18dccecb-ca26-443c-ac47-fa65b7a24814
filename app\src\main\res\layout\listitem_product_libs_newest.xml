<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="productLibsNewestItemModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsNewestItemModel" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="140dp"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        android:background="@color/clr_bg"
        android:foreground="@drawable/shape_sol_eff0f1_str_eff0f1_cor_10dp"
        app:cardBackgroundColor="@color/clr_bg"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <ImageView
                android:id="@+id/cover_iv"
                url="@{productLibsNewestItemModel.url}"
                android:layout_width="140dp"
                android:layout_height="140dp"
                android:layout_margin="1dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="W,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:singleLine="true"
                android:text="@{productLibsNewestItemModel.type}"
                android:textColor="@color/clr_main"
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/type_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="10dp"
                android:singleLine="true"
                android:text="@{productLibsNewestItemModel.name}"
                android:textSize="9sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/intro_tv"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv" />

            <TextView
                android:id="@+id/intro_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp"
                android:ellipsize="end"
                android:maxLines="4"
                android:text="@{productLibsNewestItemModel.sub}"
                android:textSize="8sp"
                app:layout_constrainedWidth="true"
                app:layout_constraintBottom_toTopOf="@id/click_view_btn"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/type_tv" />

            <com.qmuiteam.qmui.widget.roundwidget.QMUIRoundButton
                android:id="@+id/click_view_btn"
                style="@style/TooCMS.RoundButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_margin="10dp"
                android:paddingLeft="15dp"
                android:paddingTop="5dp"
                android:paddingRight="15dp"
                android:paddingBottom="5dp"
                android:text='@{@string/str_click_view}'
                android:textSize="12sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:onClickCommand="@{productLibsNewestItemModel.onItemClickBindingCommand}" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>