<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="productLibsSecondaryClassifyModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsSecondaryClassifyModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/slr_shape_sol_clr_bg_or_clr_main_cor_5dp"
        app:onClickCommand="@{productLibsSecondaryClassifyModel.onClickBindingCommand}"
        app:viewSelectStatus="@{productLibsSecondaryClassifyModel.isSelected}">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:paddingLeft="10dp"
            android:paddingTop="5dp"
            android:paddingRight="10dp"
            android:paddingBottom="5dp"
            android:text="@{productLibsSecondaryClassifyModel.smallClassify}"
            android:textColor="@drawable/clr_000000_or_ffffff"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_min="70dp" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>