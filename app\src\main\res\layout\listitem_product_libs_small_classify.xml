<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="productLibsSmallClassifyModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsSmallClassifyModel" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:background="@color/clr_bg"
        app:cardCornerRadius="10dp"
        app:cardElevation="2dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:onClickCommand="@{productLibsSmallClassifyModel.onItemClickBindingCommand}">

            <ImageView
                android:id="@+id/icon_iv"
                url="@{productLibsSmallClassifyModel.url}"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/type_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/clr_bg"
                android:gravity="center"
                android:padding="10dp"
                android:singleLine="true"
                android:text='@{productLibsSmallClassifyModel.type}'
                android:textSize="13sp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/icon_iv" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/clr_bg"
                android:gravity="center"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:paddingBottom="10dp"
                android:singleLine="true"
                android:text='@{productLibsSmallClassifyModel.name}'
                android:textSize="13sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/type_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>