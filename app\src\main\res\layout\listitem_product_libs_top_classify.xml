<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="productLibsTopClassifyModel"
            type="com.toocms.wago.ui.product_libs.ProductLibsTopClassifyModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/slr_shape_clr_bg_or_lay_bg_white_left_clr_main_line"
        app:onClickCommand="@{productLibsTopClassifyModel.onClickBindingCommand}"
        app:viewSelectStatus="@{productLibsTopClassifyModel.isSelected}">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="10dp"
            android:text="@{productLibsTopClassifyModel.product}"
            android:textColor="@drawable/clr_000000_or_clr_main"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_min="55dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>