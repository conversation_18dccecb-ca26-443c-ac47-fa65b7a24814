<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="searchResultItemModel"
            type="com.toocms.wago.ui.search.SearchResultItemModel" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        android:background="@color/clr_bg"
        app:cardBackgroundColor="@color/clr_bg"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:onClickCommand="@{searchResultItemModel.detail}">

            <ImageView
                android:id="@+id/cover_iv"
                url="@{searchResultItemModel.url}"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_margin="1dp"
                android:scaleType="centerCrop"
                android:src="@mipmap/img_default"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="W,1:1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view0"
                android:layout_width="5dp"
                android:layout_height="0dp"
                android:layout_marginLeft="15dp"
                android:background="@drawable/shape_sol_clr_main_cor_10dp"
                app:layout_constraintBottom_toBottomOf="@id/name_tv"
                app:layout_constraintDimensionRatio="H,1:1"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintTop_toTopOf="@id/name_tv" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10dp"
                android:layout_marginTop="16dp"
                android:layout_marginRight="15dp"
                android:ellipsize="end"
                android:text='@{searchResultItemModel.type+"\n"+searchResultItemModel.title}'
                android:textSize="12sp"
                android:textStyle="bold"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintLeft_toRightOf="@id/view0"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="15dp"
                android:layout_marginTop="10dp"
                android:layout_marginRight="15dp"
                android:layout_marginBottom="5dp"
                android:ellipsize="end"
                android:maxLines="3"
                android:minLines="3"
                android:text="@{searchResultItemModel.subTitle}"
                android:textSize="9sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@id/cover_iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/name_tv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>