<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="servicesListItemViewModel"
            type="com.toocms.wago.ui.about_us.services.list.ServicesListItemViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        android:background="@drawable/shape_sol_ffffffff_cor_10dp"
        android:padding="15dp"
        app:onClickCommand="@{servicesListItemViewModel.startDetail}">

        <View
            android:id="@+id/view"
            android:layout_width="5dp"
            android:layout_height="0dp"
            android:layout_marginLeft="15dp"
            android:background="@drawable/shape_sol_clr_main_cor_10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="H,1:1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="15dp"
            android:drawablePadding="10dp"
            android:gravity="center_vertical"
            android:text="@{servicesListItemViewModel.title}"
            app:drawableRightCompat="@mipmap/icon_arrow_right"
            app:layout_constraintLeft_toRightOf="@id/view"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>