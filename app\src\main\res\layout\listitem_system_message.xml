<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="systemMessageItemViewModel"
            type="com.toocms.wago.ui.mine.system_message.SystemMessageItemViewModel" />

    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="10dp"
        android:layout_marginTop="5dp"
        android:layout_marginRight="10dp"
        android:layout_marginBottom="5dp"
        app:cardCornerRadius="10dp"
        app:cardElevation="0dp">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="15dp">

            <TextView
                android:id="@+id/text1"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:layout_marginStart="15dp"
                android:background="@drawable/shape_oval"
                android:gravity="center"
                android:text="系统"
                android:textColor="@color/clr_main"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:lineSpacingExtra="3dp"
                android:text='@{systemMessageItemViewModel.title+"\n "+systemMessageItemViewModel.date}'
                app:layout_constraintBottom_toBottomOf="@id/text1"
                app:layout_constraintEnd_toStartOf="@id/close"
                app:layout_constraintStart_toEndOf="@id/text1"
                app:layout_constraintTop_toTopOf="@id/text1" />

            <ImageView
                android:id="@+id/close"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@mipmap/icon_close"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/text1" />

            <View
                android:id="@+id/divider"
                android:layout_width="match_parent"
                android:layout_height="0.5dp"
                android:layout_marginTop="10dp"
                android:background="#A4A8B0"
                app:layout_constraintTop_toBottomOf="@id/text1" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="15dp"
                android:text="@{systemMessageItemViewModel.content}"
                android:textColor="#A4A8B0"
                android:textSize="14sp"
                app:layout_constraintTop_toBottomOf="@id/divider" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</layout>