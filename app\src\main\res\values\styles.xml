<resources>

    <style name="Sample" parent="TooCMS.Compat">
        <!-- 主题色 -->
        <item name="app_primary_color">@color/clr_main</item>

        <!-- topbar -->
        <item name="qmui_skin_support_topbar_bg">@color/clr_bg</item>
        <item name="qmui_skin_support_topbar_title_color">@color/action_title_color</item>
        <item name="qmui_skin_support_topbar_subtitle_color">@color/action_title_color</item>
        <item name="qmui_skin_support_topbar_text_btn_color_state_list">@color/action_menu_color
        </item>
        <item name="qmui_skin_support_topbar_image_tint_color">@android:color/black</item>
        <item name="QMUITopBarStyle">@style/Sample.Topbar</item>

        <!-- tabSegment -->
        <item name="qmui_skin_support_tab_normal_color">#666666</item>
        <item name="qmui_skin_support_tab_selected_color">@color/clr_main</item>

        <item name="app_guide_btn_margin_bottom">90dp</item>
        <item name="app_guide_btn_padding_horizontal">25dp</item>
        <item name="app_guide_btn_padding_vertical">10dp</item>
        <item name="app_guide_btn_view_radius">25dp</item>
        <item name="app_guide_btn_view_background">
            @drawable/shape_sol_00000000_str_clr_main_cor_25dp
        </item>
        <item name="app_guide_btn_view_text_color">@color/clr_main</item>
        <item name="app_guide_btn_view_text_size">15sp</item>

        <item name="android:textSize">15sp</item>
        <item name="android:textColor">#000000</item>
        <item name="android:textColorHint">#A4A8B0</item>
    </style>

    <style name="Sample.Topbar" parent="TooCMS.TopBar">
        <item name="qmui_topbar_left_back_drawable_id">@mipmap/icon_default_back</item>
        <item name="qmui_topbar_title_text_size">14sp</item>
        <item name="qmui_topbar_title_bold">false</item>
        <item name="qmui_topbar_text_btn_bold">false</item>
    </style>


    <declare-styleable name="VerifyCodeEditText">
        <attr name="vcVerifyCodeLength" format="integer" />
        <attr name="vcBorderColor" format="color" />
        <attr name="vcBackgroundColor" format="color" />
        <attr name="vcTextColor" format="color" />
        <attr name="vcBorderRadius" format="dimension" />
        <attr name="vcTextSize" format="dimension" />
        <attr name="vcSpacing" format="dimension" />
        <attr name="vcSelectedColor" format="color" />
        <attr name="vcShowIndicator" format="boolean" />
    </declare-styleable>

</resources>