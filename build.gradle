// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        google()
        jcenter()
        maven { url "https://jitpack.io" }
//        maven { url 'https://dl.bintray.com/umsdk/release' }
//        maven { url "https://dl.bintray.com/thelasterstar/maven/" }
        maven { url 'https://developer.huawei.com/repo/' }
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.1'

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://jitpack.io" }
//        maven { url 'https://dl.bintray.com/umsdk/release' }
//        maven { url "https://dl.bintray.com/thelasterstar/maven/" }
        maven { url 'https://developer.huawei.com/repo/' }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}