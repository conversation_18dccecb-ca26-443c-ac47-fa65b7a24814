<template>
	<view id="app">
		<!-- 这里是应用的根组件 -->
	</view>
</template>

<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 检查登录状态
			this.checkLoginStatus()
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			checkLoginStatus() {
				const token = uni.getStorageSync('token')
				const isLogin = uni.getStorageSync('isLogin')
				if (!token && !isLogin) {
					// 如果没有登录，可以跳转到登录页面或设置游客模式
					console.log('用户未登录')
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	@import url("./common/common.css");
	
	#app {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
	}
</style>
