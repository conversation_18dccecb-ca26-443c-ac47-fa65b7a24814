# WAGO产品查询应用

基于uniapp开发的WAGO产品查询移动应用，支持多平台部署。

## 功能特性

- 🏠 **首页** - 产品分类展示和搜索
- 📦 **产品库** - 产品列表和筛选功能
- 📥 **下载资料** - 认证证书和产品手册下载
- 🛒 **在线商城** - WebView集成商城网站
- ℹ️ **关于我们** - 用户中心和公司信息
- 🔍 **搜索功能** - 智能搜索和历史记录
- 👤 **用户系统** - 登录注册和游客模式

## 技术栈

- **框架**: uniapp + Vue3
- **构建工具**: Vite
- **样式**: CSS3 + Flexbox
- **状态管理**: 本地存储
- **网络请求**: uni.request封装

## 项目结构

```
wago-uniapp/
├── pages/              # 页面文件
│   ├── index/         # 首页
│   ├── product/       # 产品库
│   ├── download/      # 下载资料
│   ├── shop/          # 在线商城
│   ├── about/         # 关于我们
│   ├── login/         # 登录页面
│   ├── search/        # 搜索页面
│   └── detail/        # 产品详情
├── common/            # 公共文件
│   ├── api.js         # API接口
│   ├── utils.js       # 工具函数
│   └── common.css     # 公共样式
├── static/            # 静态资源
├── App.vue            # 应用入口
├── main.js            # 主入口文件
├── pages.json         # 页面配置
├── manifest.json      # 应用配置
└── package.json       # 项目配置
```

## 开发指南

### 环境要求

- Node.js >= 16
- HBuilderX 或 VS Code
- uniapp CLI (可选)

### 快速开始

#### 方式一：直接运行演示版本
```bash
# 启动HTTP服务器
npm run serve
# 或者
python -m http.server 3000

# 浏览器访问 http://localhost:3000
```

#### 方式二：uniapp开发环境
```bash
# 安装依赖
npm install

# H5开发
npm run dev:h5

# 微信小程序开发
npm run dev:mp-weixin

# App开发
npm run dev:app
```

### 构建发布

```bash
# H5构建
npm run build:h5

# 微信小程序构建
npm run build:mp-weixin

# App构建
npm run build:app
```

## API配置

### 真实API接口
项目已配置连接到真实的WAGO API服务器：

```javascript
const BASE_URL = 'https://www.app.hemajia.net/wagoapp/wagoapp/product/'
```

### 主要API接口
- `system/banner/selectEnabledBanner` - 获取轮播图
- `product/selectByProductClass` - 获取产品分类
- `productPlan/selectShouPlan` - 获取首页计划
- `product/selectByPage` - 获取产品列表
- `product/selectByDetail` - 获取产品详情
- `product/selectBySearch` - 搜索产品
- `certificate/selectCertificateAPP` - 获取认证证书
- `productManual/selectProductManualAPP` - 获取产品手册

### API测试
演示版本已集成真实API调用，可在浏览器控制台查看API响应数据。

## 部署说明

### H5部署
1. 运行 `npm run build:h5`
2. 将 `dist/build/h5` 目录部署到Web服务器

### 微信小程序
1. 运行 `npm run build:mp-weixin`
2. 使用微信开发者工具打开 `dist/build/mp-weixin` 目录
3. 上传发布

### App打包
1. 运行 `npm run build:app`
2. 使用HBuilderX打开项目
3. 发行 -> 原生App-云打包

## 注意事项

1. 图标资源需要自行添加到 `static/tabbar/` 目录
2. API接口需要根据实际后端接口调整
3. 微信小程序需要配置合法域名
4. App打包需要配置相应的证书和包名

## 项目状态

### ✅ 已完成功能
- **UI设计** - 完全按照Android原版设计，像素级还原
- **API集成** - 连接真实WAGO API服务器，数据实时获取
- **页面结构** - 完整的5个主要页面和功能页面
- **响应式布局** - 适配移动端和桌面端
- **演示版本** - 可直接在浏览器中运行查看效果

### 🚧 开发中功能
- **uniapp编译** - 需要配置uniapp开发环境
- **图标资源** - 需要添加完整的图标文件
- **多平台适配** - 微信小程序、App等平台的特殊适配

### 📱 当前可用版本
- **H5演示版** - 在浏览器中直接运行，连接真实API
- **uniapp源码** - 完整的Vue3 + uniapp项目代码

## 更新日志

### v1.0.0 (2025-06-15)
- ✅ 完成Android UI的完整复刻
- ✅ 集成真实API接口调用
- ✅ 实现响应式布局设计
- ✅ 创建可运行的演示版本
- ✅ 支持H5、微信小程序、App多平台架构

## 许可证

MIT License
