// API配置
const BASE_URL = 'https://www.app.hemajia.net/wagoapp/wagoapp/product/'

// 请求封装
const request = (options) => {
	return new Promise((resolve, reject) => {
		// 获取token
		const token = uni.getStorageSync('token')

		// 构建完整URL
		const fullUrl = options.fullUrl || (BASE_URL + options.url)

		uni.request({
			url: fullUrl,
			method: options.method || 'POST',
			data: options.data || {},
			header: {
				'Content-Type': 'application/x-www-form-urlencoded',
				'Authorization': token ? `Bearer ${token}` : '',
				...options.header
			},
			success: (res) => {
				console.log('API Response:', res)
				if (res.statusCode === 200) {
					resolve(res.data)
				} else {
					reject(res)
				}
			},
			fail: (err) => {
				console.error('API Error:', err)
				reject(err)
			}
		})
	})
}

// API接口
export const api = {
	// 登录相关
	login: (data) => request({
		url: 'user/login',
		method: 'POST',
		data
	}),

	// 首页轮播图
	getBanners: () => request({
		url: 'system/banner/selectEnabledBanner',
		method: 'POST',
		data: {}
	}),

	// 产品分类
	getProductCategories: () => request({
		url: 'product/selectByProductClass',
		method: 'POST',
		data: {}
	}),

	// 首页计划
	getShouPlan: () => request({
		url: 'productPlan/selectShouPlan',
		method: 'POST',
		data: {}
	}),

	// 产品列表
	getProducts: (data) => request({
		url: 'product/selectByPage',
		method: 'POST',
		data
	}),

	// 产品详情
	getProductDetail: (productId) => request({
		url: 'product/selectByDetail',
		method: 'POST',
		data: { productId }
	}),

	// 搜索产品
	searchProducts: (data) => request({
		url: 'product/selectBySearch',
		method: 'POST',
		data
	}),

	// 认证证书
	getCertificates: (data) => request({
		url: 'certificate/selectCertificateAPP',
		method: 'POST',
		data
	}),

	// 产品手册
	getManuals: (data) => request({
		url: 'productManual/selectProductManualAPP',
		method: 'POST',
		data
	}),

	// 关于我们
	getAboutUs: () => request({
		url: 'content/selectByDetail',
		method: 'POST',
		data: {}
	}),

	// 版本检查
	checkVersion: () => request({
		url: 'System/checkVersion',
		method: 'POST',
		data: {}
	}),

	// 发送验证码
	sendCode: (data) => request({
		url: 'user/sendCode',
		method: 'POST',
		data
	})
}

export default api
