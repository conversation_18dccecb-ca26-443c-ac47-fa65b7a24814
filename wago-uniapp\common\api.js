// API配置
const BASE_URL = 'https://www.app.hemajia.net/wagoapp/wagoapp/product/'

// 请求封装
const request = (options) => {
	return new Promise((resolve, reject) => {
		// 获取token
		const token = uni.getStorageSync('token')
		
		uni.request({
			url: BASE_URL + options.url,
			method: options.method || 'GET',
			data: options.data || {},
			header: {
				'Content-Type': options.method === 'POST' ? 'application/json' : 'application/x-www-form-urlencoded',
				'Authorization': token ? `Bearer ${token}` : '',
				...options.header
			},
			success: (res) => {
				if (res.statusCode === 200) {
					resolve(res.data)
				} else {
					reject(res)
				}
			},
			fail: (err) => {
				reject(err)
			}
		})
	})
}

// API接口
export const api = {
	// 登录相关
	login: (data) => request({
		url: 'user/login',
		method: 'POST',
		data
	}),
	
	// 首页轮播图
	getBanners: () => request({
		url: 'system/banner/selectEnabledBanner',
		method: 'POST'
	}),
	
	// 产品分类
	getProductCategories: () => request({
		url: 'product/selectByProductClass',
		method: 'POST'
	}),
	
	// 产品列表
	getProducts: (data) => request({
		url: 'product/selectByPage',
		method: 'POST',
		data
	}),
	
	// 产品详情
	getProductDetail: (productId) => request({
		url: 'product/selectByDetail',
		method: 'POST',
		data: { productId }
	}),
	
	// 搜索产品
	searchProducts: (data) => request({
		url: 'product/selectBySearch',
		method: 'POST',
		data
	}),
	
	// 认证证书
	getCertificates: (data) => request({
		url: 'certificate/selectCertificateAPP',
		method: 'POST',
		data
	}),
	
	// 产品手册
	getManuals: (data) => request({
		url: 'productManual/selectProductManualAPP',
		method: 'POST',
		data
	}),
	
	// 关于我们
	getAboutUs: () => request({
		url: 'content/selectByDetail',
		method: 'POST'
	}),
	
	// 版本检查
	checkVersion: () => request({
		url: 'System/checkVersion',
		method: 'POST'
	})
}

export default api
