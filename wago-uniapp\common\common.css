/* 公共样式 */
* {
	box-sizing: border-box;
}

page {
	background-color: #f5f5f5;
	font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* 通用容器 */
.container {
	padding: 0 20rpx;
}

/* 通用按钮 */
.btn {
	display: inline-block;
	padding: 20rpx 40rpx;
	border-radius: 8rpx;
	text-align: center;
	font-size: 28rpx;
	border: none;
	cursor: pointer;
}

.btn-primary {
	background-color: #007aff;
	color: white;
}

.btn-secondary {
	background-color: #f8f8f8;
	color: #333;
}

/* 卡片样式 */
.card {
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
	margin: 20rpx 0;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

/* 列表项 */
.list-item {
	display: flex;
	align-items: center;
	padding: 30rpx 20rpx;
	background-color: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.list-item:last-child {
	border-bottom: none;
}

/* 文本样式 */
.text-primary {
	color: #007aff;
}

.text-secondary {
	color: #666;
}

.text-muted {
	color: #999;
}

.text-center {
	text-align: center;
}

.text-right {
	text-align: right;
}

/* 间距 */
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }

.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* Flex布局 */
.flex {
	display: flex;
}

.flex-column {
	flex-direction: column;
}

.flex-center {
	justify-content: center;
	align-items: center;
}

.flex-between {
	justify-content: space-between;
}

.flex-around {
	justify-content: space-around;
}

.flex-1 {
	flex: 1;
}

/* 图片样式 */
.img-responsive {
	width: 100%;
	height: auto;
}

/* 搜索框 */
.search-box {
	display: flex;
	align-items: center;
	background-color: #f5f5f5;
	border-radius: 50rpx;
	padding: 20rpx 30rpx;
	margin: 20rpx;
}

.search-input {
	flex: 1;
	border: none;
	background: transparent;
	font-size: 28rpx;
}

/* 标签 */
.tag {
	display: inline-block;
	padding: 10rpx 20rpx;
	background-color: #f0f0f0;
	color: #666;
	border-radius: 20rpx;
	font-size: 24rpx;
	margin: 5rpx;
}

.tag-active {
	background-color: #007aff;
	color: white;
}

/* 空状态 */
.empty-state {
	text-align: center;
	padding: 100rpx 20rpx;
	color: #999;
}

.empty-state image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 30rpx;
}

/* 加载状态 */
.loading {
	text-align: center;
	padding: 40rpx;
	color: #999;
}

/* 自定义导航栏 */
.custom-navbar {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	z-index: 999;
	background-color: white;
	border-bottom: 1rpx solid #f0f0f0;
}

.navbar-content {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx;
	height: 88rpx;
}

.navbar-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

/* 页面内容区域，避免被固定导航栏遮挡 */
.page-content {
	padding-top: 108rpx;
}
