// 工具函数

// 显示提示信息
export const showToast = (title, icon = 'none', duration = 2000) => {
	uni.showToast({
		title,
		icon,
		duration
	})
}

// 显示加载中
export const showLoading = (title = '加载中...') => {
	uni.showLoading({
		title,
		mask: true
	})
}

// 隐藏加载中
export const hideLoading = () => {
	uni.hideLoading()
}

// 确认对话框
export const showModal = (content, title = '提示') => {
	return new Promise((resolve) => {
		uni.showModal({
			title,
			content,
			success: (res) => {
				resolve(res.confirm)
			}
		})
	})
}

// 页面跳转
export const navigateTo = (url, params = {}) => {
	let query = ''
	if (Object.keys(params).length > 0) {
		query = '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
	}
	uni.navigateTo({
		url: url + query
	})
}

// 返回上一页
export const navigateBack = (delta = 1) => {
	uni.navigateBack({
		delta
	})
}

// 切换到tabBar页面
export const switchTab = (url) => {
	uni.switchTab({
		url
	})
}

// 格式化日期
export const formatDate = (date, format = 'YYYY-MM-DD') => {
	const d = new Date(date)
	const year = d.getFullYear()
	const month = String(d.getMonth() + 1).padStart(2, '0')
	const day = String(d.getDate()).padStart(2, '0')
	const hour = String(d.getHours()).padStart(2, '0')
	const minute = String(d.getMinutes()).padStart(2, '0')
	const second = String(d.getSeconds()).padStart(2, '0')
	
	return format
		.replace('YYYY', year)
		.replace('MM', month)
		.replace('DD', day)
		.replace('HH', hour)
		.replace('mm', minute)
		.replace('ss', second)
}

// 防抖函数
export const debounce = (func, wait) => {
	let timeout
	return function executedFunction(...args) {
		const later = () => {
			clearTimeout(timeout)
			func(...args)
		}
		clearTimeout(timeout)
		timeout = setTimeout(later, wait)
	}
}

// 节流函数
export const throttle = (func, limit) => {
	let inThrottle
	return function() {
		const args = arguments
		const context = this
		if (!inThrottle) {
			func.apply(context, args)
			inThrottle = true
			setTimeout(() => inThrottle = false, limit)
		}
	}
}

// 获取系统信息
export const getSystemInfo = () => {
	return new Promise((resolve) => {
		uni.getSystemInfo({
			success: (res) => {
				resolve(res)
			}
		})
	})
}

// 获取状态栏高度
export const getStatusBarHeight = async () => {
	const systemInfo = await getSystemInfo()
	return systemInfo.statusBarHeight || 0
}

// 下载文件
export const downloadFile = (url, filename) => {
	return new Promise((resolve, reject) => {
		uni.downloadFile({
			url,
			success: (res) => {
				if (res.statusCode === 200) {
					// 保存到相册或文件系统
					uni.saveFile({
						tempFilePath: res.tempFilePath,
						success: (saveRes) => {
							showToast('下载成功', 'success')
							resolve(saveRes)
						},
						fail: (err) => {
							showToast('保存失败')
							reject(err)
						}
					})
				} else {
					showToast('下载失败')
					reject(res)
				}
			},
			fail: (err) => {
				showToast('下载失败')
				reject(err)
			}
		})
	})
}

// 分享功能
export const shareContent = (title, path, imageUrl) => {
	return new Promise((resolve, reject) => {
		uni.share({
			provider: 'weixin',
			scene: 'WXSceneSession',
			type: 0,
			href: path,
			title,
			summary: title,
			imageUrl,
			success: (res) => {
				showToast('分享成功', 'success')
				resolve(res)
			},
			fail: (err) => {
				showToast('分享失败')
				reject(err)
			}
		})
	})
}

// 存储相关
export const storage = {
	set: (key, value) => {
		try {
			uni.setStorageSync(key, value)
		} catch (e) {
			console.error('存储失败:', e)
		}
	},
	get: (key, defaultValue = null) => {
		try {
			return uni.getStorageSync(key) || defaultValue
		} catch (e) {
			console.error('读取存储失败:', e)
			return defaultValue
		}
	},
	remove: (key) => {
		try {
			uni.removeStorageSync(key)
		} catch (e) {
			console.error('删除存储失败:', e)
		}
	},
	clear: () => {
		try {
			uni.clearStorageSync()
		} catch (e) {
			console.error('清空存储失败:', e)
		}
	}
}

// 用户登录状态管理
export const auth = {
	// 检查是否登录
	isLogin: () => {
		const token = storage.get('token')
		const isLogin = storage.get('isLogin')
		return !!(token || isLogin)
	},
	
	// 设置登录状态
	setLogin: (token, userInfo) => {
		storage.set('token', token)
		storage.set('userInfo', userInfo)
		storage.set('isLogin', true)
	},
	
	// 退出登录
	logout: () => {
		storage.remove('token')
		storage.remove('userInfo')
		storage.remove('isLogin')
	},
	
	// 获取用户信息
	getUserInfo: () => {
		return storage.get('userInfo', {})
	}
}
