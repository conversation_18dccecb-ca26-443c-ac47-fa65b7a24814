<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WAGO产品查询</title>
    <meta name="description" content="WAGO产品查询应用，连接电气工程技术">
    <meta name="keywords" content="WAGO,产品查询,电气工程,自动化">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .app {
            max-width: 414px;
            margin: 0 auto;
            background-color: #f5f5f5;
            min-height: 100vh;
            position: relative;
        }

        /* 搜索栏 */
        .search-header {
            background-color: #ffffff;
            border-bottom: 1px solid #e5e5e5;
            padding: 15px;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .search-box {
            display: flex;
            align-items: center;
            background-color: #f5f5f5;
            border-radius: 6px;
            padding: 12px 15px;
            height: 40px;
        }

        .search-icon {
            width: 16px;
            height: 16px;
            margin-right: 10px;
            opacity: 0.6;
        }

        .search-text {
            font-size: 14px;
            color: #999999;
        }

        /* 用户信息区域 */
        .user-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background-color: #ffffff;
            border-bottom: 1px solid #e5e5e5;
        }

        .welcome-text {
            flex: 1;
        }

        .welcome-title {
            display: block;
            font-size: 14px;
            color: #666666;
            margin-bottom: 5px;
        }

        .welcome-subtitle {
            display: block;
            font-size: 16px;
            font-weight: bold;
            color: #333333;
        }

        .user-center {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 5px;
            cursor: pointer;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: #f0f0f0;
            margin-bottom: 5px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
            background-size: 60%;
            background-repeat: no-repeat;
            background-position: center;
        }

        .user-center-text {
            font-size: 12px;
            color: #666666;
        }

        /* 功能网格 */
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .feature-item:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .feature-desc {
            font-size: 12px;
            color: #666;
        }

        /* 轮播图 */
        .banner-section {
            margin: 15px;
        }

        .banner-image {
            width: 100%;
            height: 150px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            margin-bottom: 15px;
        }

        /* 产品分类 */
        .product-section {
            background-color: #ffffff;
            padding: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333333;
        }

        .product-grid {
            display: flex;
            flex-wrap: wrap;
            margin: -5px;
        }

        .product-item {
            width: 50%;
            padding: 5px;
            box-sizing: border-box;
        }

        .product-card {
            background-color: #f8f8f8;
            border-radius: 8px;
            padding: 20px 15px;
            text-align: center;
            position: relative;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .product-card:hover {
            transform: translateY(-2px);
        }

        .product-image {
            width: 40px;
            height: 40px;
            background-color: #e0e0e0;
            border-radius: 6px;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .product-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .product-name {
            font-size: 13px;
            color: #333333;
            font-weight: 500;
            flex: 1;
            text-align: left;
        }

        .arrow-icon {
            width: 12px;
            height: 12px;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23999"><path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            padding: 10px 0;
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 5px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #007aff;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-text {
            font-size: 12px;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .app {
                max-width: 100%;
            }

            .bottom-nav {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 搜索栏 -->
        <div class="search-header">
            <div class="search-box" onclick="showMessage('搜索功能')">
                <div class="search-icon"></div>
                <span class="search-text">Search</span>
            </div>
        </div>

        <!-- 用户信息区域 -->
        <div class="user-section">
            <div class="welcome-text">
                <span class="welcome-title">欢迎进入</span>
                <span class="welcome-subtitle">WAGO Product Catalogue</span>
            </div>
            <div class="user-center" onclick="showMessage('个人中心')">
                <div class="user-avatar"></div>
                <span class="user-center-text">个人中心</span>
            </div>
        </div>

        <!-- 轮播图 -->
        <div class="banner-section">
            <div class="banner-image" onclick="showMessage('轮播图')">
                WAGO 产品展示
            </div>
        </div>

        <!-- 产品分类 -->
        <div class="product-section">
            <div class="section-title">产品分类</div>
            <div class="product-grid">
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('I/O系统')">
                        <div class="product-image">🔌</div>
                        <div class="product-info">
                            <span class="product-name">I/O系统</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('控制器')">
                        <div class="product-image">🎛️</div>
                        <div class="product-info">
                            <span class="product-name">控制器</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('接线端子')">
                        <div class="product-image">🔗</div>
                        <div class="product-info">
                            <span class="product-name">接线端子</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('电源模块')">
                        <div class="product-image">⚡</div>
                        <div class="product-info">
                            <span class="product-name">电源模块</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('通讯模块')">
                        <div class="product-image">📡</div>
                        <div class="product-info">
                            <span class="product-name">通讯模块</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
                <div class="product-item">
                    <div class="product-card" onclick="showMessage('软件工具')">
                        <div class="product-image">💻</div>
                        <div class="product-info">
                            <span class="product-name">软件工具</span>
                            <div class="arrow-icon"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchTab(this, '首页')">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '产品库')">
                <div class="nav-icon">📦</div>
                <div class="nav-text">产品库</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '下载资料')">
                <div class="nav-icon">📥</div>
                <div class="nav-text">下载资料</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '在线商城')">
                <div class="nav-icon">🛒</div>
                <div class="nav-text">在线商城</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '关于我们')">
                <div class="nav-icon">ℹ️</div>
                <div class="nav-text">关于我们</div>
            </div>
        </div>
    </div>

    <script>
        // API配置
        const BASE_URL = 'https://www.app.hemajia.net/wagoapp/wagoapp/product/';

        // 请求封装
        function request(url, data = {}) {
            return fetch(BASE_URL + url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams(data)
            })
            .then(response => response.json())
            .catch(error => {
                console.error('API Error:', error);
                return { code: 500, message: '网络错误' };
            });
        }

        // 加载轮播图
        function loadBanners() {
            request('system/banner/selectEnabledBanner')
                .then(data => {
                    console.log('轮播图数据:', data);
                    if (data.code === 200 && data.data) {
                        updateBanners(data.data);
                    }
                });
        }

        // 加载产品分类
        function loadProducts() {
            request('product/selectByProductClass')
                .then(data => {
                    console.log('产品分类数据:', data);
                    if (data.code === 200 && data.data) {
                        updateProducts(data.data);
                    }
                });
        }

        // 更新轮播图
        function updateBanners(banners) {
            const bannerElement = document.querySelector('.banner-image');
            if (banners.length > 0 && bannerElement) {
                bannerElement.style.backgroundImage = `url(${banners[0].imgUrl})`;
                bannerElement.style.backgroundSize = 'cover';
                bannerElement.style.backgroundPosition = 'center';
                bannerElement.textContent = '';
            }
        }

        // 更新产品分类
        function updateProducts(products) {
            const productGrid = document.querySelector('.product-grid');
            if (products.length > 0 && productGrid) {
                productGrid.innerHTML = '';
                products.forEach(product => {
                    const productItem = document.createElement('div');
                    productItem.className = 'product-item';
                    productItem.innerHTML = `
                        <div class="product-card" onclick="showMessage('${product.productCategoryName}')">
                            <div class="product-image">
                                <img src="${product.imgUrl || product.imgurl}" alt="${product.productCategoryName}" style="width: 100%; height: 100%; object-fit: contain;" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div style="display: none; width: 100%; height: 100%; align-items: center; justify-content: center; background: #f0f0f0; border-radius: 6px;">📦</div>
                            </div>
                            <div class="product-info">
                                <span class="product-name">${product.productCategoryName}</span>
                                <div class="arrow-icon"></div>
                            </div>
                        </div>
                    `;
                    productGrid.appendChild(productItem);
                });
            }
        }

        function showMessage(feature) {
            alert('点击了：' + feature + '\n\n这是WAGO产品查询应用的演示版本。\n\n✅ 已连接真实API接口\n✅ 数据来源：' + BASE_URL + '\n\n完整功能请使用uniapp开发版本。');
        }

        function switchTab(element, tabName) {
            // 移除所有active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加active类到当前项
            element.classList.add('active');

            // 显示消息
            showMessage(tabName + '页面');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('WAGO产品查询应用演示版本已加载');
            console.log('正在加载真实API数据...');

            // 加载真实数据
            loadBanners();
            loadProducts();
        });
    </script>
</body>
</html>
