<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>WAGO产品查询</title>
    <meta name="description" content="WAGO产品查询应用，连接电气工程技术">
    <meta name="keywords" content="WAGO,产品查询,电气工程,自动化">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .app {
            max-width: 414px;
            margin: 0 auto;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        /* 导航栏 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .navbar h1 {
            font-size: 20px;
            margin-bottom: 5px;
        }

        .navbar p {
            font-size: 14px;
            opacity: 0.9;
        }

        /* 主要内容 */
        .main-content {
            padding: 20px;
        }

        /* 用户信息卡片 */
        .user-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-size: 20px;
            font-weight: bold;
        }

        .user-info h3 {
            margin-bottom: 5px;
        }

        .user-info p {
            font-size: 14px;
            opacity: 0.8;
        }

        /* 功能网格 */
        .feature-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: transform 0.2s;
        }

        .feature-item:hover {
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 30px;
            margin-bottom: 10px;
        }

        .feature-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .feature-desc {
            font-size: 12px;
            color: #666;
        }

        /* 产品列表 */
        .product-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .product-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .product-item {
            background: white;
            padding: 15px;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-align: center;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .product-item:hover {
            transform: translateY(-2px);
        }

        .product-image {
            width: 60px;
            height: 60px;
            background-color: #f0f0f0;
            border-radius: 8px;
            margin: 0 auto 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .product-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .product-desc {
            font-size: 12px;
            color: #666;
        }

        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #f0f0f0;
            display: flex;
            padding: 10px 0;
        }

        .nav-item {
            flex: 1;
            text-align: center;
            padding: 5px;
            cursor: pointer;
            transition: color 0.2s;
        }

        .nav-item.active {
            color: #007aff;
        }

        .nav-icon {
            font-size: 20px;
            margin-bottom: 3px;
        }

        .nav-text {
            font-size: 12px;
        }

        /* 响应式 */
        @media (max-width: 480px) {
            .app {
                max-width: 100%;
            }

            .bottom-nav {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="app">
        <!-- 导航栏 -->
        <div class="navbar">
            <h1>WAGO产品查询</h1>
            <p>连接电气工程技术</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <!-- 用户信息 -->
            <div class="user-card">
                <div class="user-avatar">游</div>
                <div class="user-info">
                    <h3>游客用户</h3>
                    <p>点击登录获得更好体验</p>
                </div>
            </div>

            <!-- 功能网格 -->
            <div class="feature-grid">
                <div class="feature-item" onclick="showMessage('搜索功能')">
                    <div class="feature-icon">🔍</div>
                    <div class="feature-title">智能搜索</div>
                    <div class="feature-desc">快速查找产品</div>
                </div>
                <div class="feature-item" onclick="showMessage('下载功能')">
                    <div class="feature-icon">📥</div>
                    <div class="feature-title">资料下载</div>
                    <div class="feature-desc">证书和手册</div>
                </div>
                <div class="feature-item" onclick="showMessage('收藏功能')">
                    <div class="feature-icon">❤️</div>
                    <div class="feature-title">我的收藏</div>
                    <div class="feature-desc">收藏喜欢的产品</div>
                </div>
                <div class="feature-item" onclick="showMessage('商城功能')">
                    <div class="feature-icon">🛒</div>
                    <div class="feature-title">在线商城</div>
                    <div class="feature-desc">在线购买产品</div>
                </div>
            </div>

            <!-- 产品分类 -->
            <div class="product-section">
                <div class="section-title">产品分类</div>
                <div class="product-list">
                    <div class="product-item" onclick="showMessage('I/O系统')">
                        <div class="product-image">🔌</div>
                        <div class="product-name">I/O系统</div>
                        <div class="product-desc">输入输出模块</div>
                    </div>
                    <div class="product-item" onclick="showMessage('控制器')">
                        <div class="product-image">🎛️</div>
                        <div class="product-name">控制器</div>
                        <div class="product-desc">PLC控制器</div>
                    </div>
                    <div class="product-item" onclick="showMessage('接线端子')">
                        <div class="product-image">🔗</div>
                        <div class="product-name">接线端子</div>
                        <div class="product-desc">连接解决方案</div>
                    </div>
                    <div class="product-item" onclick="showMessage('电源模块')">
                        <div class="product-image">⚡</div>
                        <div class="product-name">电源模块</div>
                        <div class="product-desc">电源供应</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部导航 -->
        <div class="bottom-nav">
            <div class="nav-item active" onclick="switchTab(this, '首页')">
                <div class="nav-icon">🏠</div>
                <div class="nav-text">首页</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '产品库')">
                <div class="nav-icon">📦</div>
                <div class="nav-text">产品库</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '下载资料')">
                <div class="nav-icon">📥</div>
                <div class="nav-text">下载资料</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '在线商城')">
                <div class="nav-icon">🛒</div>
                <div class="nav-text">在线商城</div>
            </div>
            <div class="nav-item" onclick="switchTab(this, '关于我们')">
                <div class="nav-icon">ℹ️</div>
                <div class="nav-text">关于我们</div>
            </div>
        </div>
    </div>

    <script>
        function showMessage(feature) {
            alert('点击了：' + feature + '\n\n这是WAGO产品查询应用的演示版本。\n完整功能请使用uniapp开发版本。');
        }

        function switchTab(element, tabName) {
            // 移除所有active类
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // 添加active类到当前项
            element.classList.add('active');

            // 显示消息
            showMessage(tabName + '页面');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('WAGO产品查询应用演示版本已加载');
        });
    </script>
</body>
</html>
