{"name": "WAGO产品查询", "appid": "__UNI__WAGO", "description": "WAGO产品查询应用", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />", "<uses-permission android:name=\"android.permission.VIBRATE\" />", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />", "<uses-permission android:name=\"android.permission.INTERNET\" />", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />", "<uses-permission android:name=\"android.permission.CAMERA\" />", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />", "<uses-permission android:name=\"android.permission.READ_CONTACTS\" />", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\" />", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\" />", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\" />", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\" />", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />"]}, "ios": {}, "sdkConfigs": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "h5": {"title": "WAGO产品查询", "template": "index.html"}}