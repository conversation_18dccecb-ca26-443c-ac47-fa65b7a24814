{"name": "wago-uniapp", "version": "1.0.0", "description": "WAGO产品查询应用", "main": "main.js", "scripts": {"dev:h5": "uni", "build:h5": "uni build", "dev:mp-weixin": "uni -p mp-weixin", "build:mp-weixin": "uni build -p mp-weixin", "dev:app": "uni -p app", "build:app": "uni build -p app", "serve": "python -m http.server 3000"}, "keywords": ["uniapp", "vue3", "wago", "product"], "author": "WAGO Team", "license": "MIT", "dependencies": {"vue": "^3.3.0"}, "devDependencies": {"vite": "^4.4.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}