{"name": "wago-uniapp", "version": "1.0.0", "description": "WAGO产品查询应用", "main": "main.js", "scripts": {"dev:h5": "uni build --watch --platform h5", "build:h5": "uni build --platform h5", "dev:mp-weixin": "uni build --watch --platform mp-weixin", "build:mp-weixin": "uni build --platform mp-weixin", "dev:app": "uni build --watch --platform app", "build:app": "uni build --platform app"}, "keywords": ["uniapp", "vue3", "wago", "product"], "author": "WAGO Team", "license": "MIT", "dependencies": {"@dcloudio/uni-app": "^3.0.0", "@dcloudio/uni-components": "^3.0.0", "@dcloudio/uni-h5": "^3.0.0", "@dcloudio/uni-mp-weixin": "^3.0.0", "vue": "^3.2.0"}, "devDependencies": {"@dcloudio/uni-cli-shared": "^3.0.0", "@dcloudio/vite-plugin-uni": "^3.0.0", "vite": "^4.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}