<template>
	<view class="about-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">关于我们</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 用户信息区域 -->
			<view class="user-section card" v-if="isLogin">
				<view class="user-info">
					<view class="user-avatar">
						<text class="avatar-text">{{ userInfo.name ? userInfo.name.charAt(0) : 'U' }}</text>
					</view>
					<view class="user-details">
						<text class="user-name">{{ userInfo.name || '用户' }}</text>
						<text class="user-desc">{{ userInfo.email || '暂无邮箱' }}</text>
					</view>
					<view class="user-actions">
						<button class="logout-btn" @click="logout">退出登录</button>
					</view>
				</view>
			</view>
			
			<!-- 游客状态 -->
			<view class="guest-section card" v-else>
				<view class="guest-info">
					<view class="guest-avatar">
						<text class="avatar-text">游</text>
					</view>
					<view class="guest-details">
						<text class="guest-name">游客用户</text>
						<text class="guest-desc">登录后享受更多功能</text>
					</view>
					<view class="guest-actions">
						<button class="login-btn" @click="goToLogin">立即登录</button>
					</view>
				</view>
			</view>
			
			<!-- 功能菜单 -->
			<view class="menu-section">
				<view class="menu-group">
					<view class="menu-item" @click="goToMyCollect">
						<view class="menu-icon">❤️</view>
						<text class="menu-text">我的收藏</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToMyShare">
						<view class="menu-icon">📤</view>
						<text class="menu-text">我的分享</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToBrowseRecord">
						<view class="menu-icon">👁️</view>
						<text class="menu-text">浏览记录</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToDownloadRecord">
						<view class="menu-icon">📥</view>
						<text class="menu-text">下载记录</text>
						<text class="menu-arrow">></text>
					</view>
				</view>
				
				<view class="menu-group">
					<view class="menu-item" @click="goToSystemMessage">
						<view class="menu-icon">📢</view>
						<text class="menu-text">系统消息</text>
						<view class="message-badge" v-if="unreadCount > 0">{{ unreadCount }}</view>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToWagoDetail">
						<view class="menu-icon">🏢</view>
						<text class="menu-text">WAGO集团</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToServices">
						<view class="menu-icon">🛠️</view>
						<text class="menu-text">服务支持</text>
						<text class="menu-arrow">></text>
					</view>
				</view>
				
				<view class="menu-group">
					<view class="menu-item" @click="checkUpdate">
						<view class="menu-icon">🔄</view>
						<text class="menu-text">检查更新</text>
						<text class="menu-desc">v{{ appVersion }}</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="goToFeedback">
						<view class="menu-icon">💬</view>
						<text class="menu-text">意见反馈</text>
						<text class="menu-arrow">></text>
					</view>
				</view>
			</view>
			
			<!-- 公司信息 -->
			<view class="company-section card">
				<view class="company-header">
					<image class="company-logo" src="/static/logo.png" mode="aspectFit"></image>
					<view class="company-info">
						<text class="company-name">WAGO</text>
						<text class="company-desc">连接电气工程技术</text>
					</view>
				</view>
				<view class="company-content">
					<text class="company-text">
						WAGO是全球领先的连接技术和自动化解决方案提供商，致力于为客户提供创新的产品和服务。
					</text>
				</view>
				<view class="company-actions">
					<button class="contact-btn" @click="contactUs">联系我们</button>
					<button class="website-btn" @click="visitWebsite">官方网站</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, showModal, navigateTo, getStatusBarHeight, auth } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				isLogin: false,
				userInfo: {},
				unreadCount: 0,
				appVersion: '1.0.0'
			}
		},
		
		async onLoad() {
			this.statusBarHeight = await getStatusBarHeight()
			this.checkLoginStatus()
			this.loadUnreadCount()
		},
		
		onShow() {
			this.checkLoginStatus()
			this.loadUnreadCount()
		},
		
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				this.isLogin = auth.isLogin()
				this.userInfo = auth.getUserInfo()
			},
			
			// 加载未读消息数量
			async loadUnreadCount() {
				try {
					// 这里应该调用API获取未读消息数量
					// const res = await api.getUnreadCount()
					// this.unreadCount = res.data || 0
					this.unreadCount = 3 // 模拟数据
				} catch (error) {
					console.error('获取未读消息数量失败:', error)
				}
			},
			
			// 退出登录
			async logout() {
				const confirm = await showModal('确定要退出登录吗？')
				if (confirm) {
					auth.logout()
					this.checkLoginStatus()
					showToast('已退出登录', 'success')
				}
			},
			
			// 跳转到登录页面
			goToLogin() {
				navigateTo('/pages/login/login')
			},
			
			// 我的收藏
			goToMyCollect() {
				if (!this.checkLoginRequired()) return
				navigateTo('/pages/collect/collect')
			},
			
			// 我的分享
			goToMyShare() {
				if (!this.checkLoginRequired()) return
				navigateTo('/pages/share/share')
			},
			
			// 浏览记录
			goToBrowseRecord() {
				navigateTo('/pages/record/browse')
			},
			
			// 下载记录
			goToDownloadRecord() {
				navigateTo('/pages/record/download')
			},
			
			// 系统消息
			goToSystemMessage() {
				if (!this.checkLoginRequired()) return
				navigateTo('/pages/message/message')
			},
			
			// WAGO集团详情
			goToWagoDetail() {
				navigateTo('/pages/detail/wago')
			},
			
			// 服务支持
			goToServices() {
				navigateTo('/pages/services/services')
			},
			
			// 检查更新
			async checkUpdate() {
				try {
					showToast('检查更新中...', 'loading')
					const res = await api.checkVersion()
					if (res.code === 200 && res.data) {
						const { hasUpdate, version, downloadUrl } = res.data
						if (hasUpdate) {
							const confirm = await showModal(`发现新版本 v${version}，是否立即更新？`, '版本更新')
							if (confirm) {
								// 跳转到下载页面或应用商店
								// 这里可以实现应用内更新逻辑
								showToast('正在跳转到更新页面...')
							}
						} else {
							showToast('已是最新版本', 'success')
						}
					}
				} catch (error) {
					console.error('检查更新失败:', error)
					showToast('检查更新失败')
				}
			},
			
			// 意见反馈
			goToFeedback() {
				navigateTo('/pages/feedback/feedback')
			},
			
			// 联系我们
			contactUs() {
				// 可以打开拨号界面或发送邮件
				uni.makePhoneCall({
					phoneNumber: '************',
					fail: () => {
						showToast('拨号失败')
					}
				})
			},
			
			// 访问官网
			visitWebsite() {
				navigateTo('/pages/webview/webview', {
					url: 'https://www.wago.com',
					title: 'WAGO官网'
				})
			},
			
			// 检查是否需要登录
			checkLoginRequired() {
				if (!this.isLogin) {
					showModal('该功能需要登录后使用，是否立即登录？').then(confirm => {
						if (confirm) {
							this.goToLogin()
						}
					})
					return false
				}
				return true
			}
		}
	}
</script>

<style scoped>
	.about-page {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 用户信息区域 */
	.user-section, .guest-section {
		margin: 20rpx;
		padding: 30rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border-radius: 20rpx;
	}
	
	.user-info, .guest-info {
		display: flex;
		align-items: center;
	}
	
	.user-avatar, .guest-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.avatar-text {
		font-size: 32rpx;
		font-weight: bold;
		color: white;
	}
	
	.user-details, .guest-details {
		flex: 1;
	}
	
	.user-name, .guest-name {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.user-desc, .guest-desc {
		display: block;
		font-size: 24rpx;
		opacity: 0.8;
	}
	
	.user-actions, .guest-actions {
		margin-left: 20rpx;
	}
	
	.logout-btn, .login-btn {
		padding: 15rpx 30rpx;
		background-color: rgba(255, 255, 255, 0.2);
		color: white;
		border-radius: 20rpx;
		font-size: 24rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}
	
	/* 菜单区域 */
	.menu-section {
		margin: 20rpx;
	}
	
	.menu-group {
		background-color: white;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
	}
	
	.menu-item {
		display: flex;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f8f8f8;
		position: relative;
	}
	
	.menu-item:last-child {
		border-bottom: none;
	}
	
	.menu-item:active {
		background-color: #f8f8f8;
	}
	
	.menu-icon {
		width: 50rpx;
		text-align: center;
		font-size: 32rpx;
		margin-right: 20rpx;
	}
	
	.menu-text {
		flex: 1;
		font-size: 30rpx;
		color: #333;
	}
	
	.menu-desc {
		font-size: 24rpx;
		color: #999;
		margin-right: 10rpx;
	}
	
	.menu-arrow {
		font-size: 24rpx;
		color: #ccc;
	}
	
	.message-badge {
		background-color: #ff4757;
		color: white;
		font-size: 20rpx;
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		margin-right: 10rpx;
		min-width: 32rpx;
		text-align: center;
	}
	
	/* 公司信息 */
	.company-section {
		margin: 20rpx;
		padding: 30rpx;
	}
	
	.company-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}
	
	.company-logo {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}
	
	.company-info {
		flex: 1;
	}
	
	.company-name {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 5rpx;
	}
	
	.company-desc {
		display: block;
		font-size: 24rpx;
		color: #666;
	}
	
	.company-content {
		margin-bottom: 30rpx;
	}
	
	.company-text {
		font-size: 26rpx;
		color: #666;
		line-height: 1.6;
	}
	
	.company-actions {
		display: flex;
		gap: 20rpx;
	}
	
	.contact-btn, .website-btn {
		flex: 1;
		padding: 25rpx;
		border-radius: 25rpx;
		text-align: center;
		font-size: 26rpx;
		border: none;
	}
	
	.contact-btn {
		background-color: #007aff;
		color: white;
	}
	
	.website-btn {
		background-color: #f8f8f8;
		color: #666;
	}
</style>
