<template>
	<view class="detail-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="navbar-title">{{ pageTitle }}</text>
				<view class="navbar-actions">
					<text class="action-btn" @click="toggleCollect">{{ isCollected ? '❤️' : '🤍' }}</text>
					<text class="action-btn" @click="shareProduct">📤</text>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 产品轮播图 -->
			<view class="banner-section" v-if="productDetail.banners && productDetail.banners.length > 0">
				<swiper class="banner-swiper" indicator-dots autoplay circular>
					<swiper-item v-for="(banner, index) in productDetail.banners" :key="index">
						<image class="banner-image" :src="banner.imageUrl" mode="aspectFill" @click="previewImage(banner.imageUrl)"></image>
					</swiper-item>
				</swiper>
			</view>
			
			<!-- 产品基本信息 -->
			<view class="product-info card">
				<text class="product-name">{{ productDetail.productName }}</text>
				<text class="product-code">产品编号：{{ productDetail.productCode }}</text>
				<text class="product-desc">{{ productDetail.productDesc }}</text>
				
				<view class="product-tags">
					<text class="tag" v-if="productDetail.productType">{{ productDetail.productType }}</text>
					<text class="tag" v-if="productDetail.productSeries">{{ productDetail.productSeries }}</text>
					<text class="tag" v-if="productDetail.productFunction">{{ productDetail.productFunction }}</text>
				</view>
			</view>
			
			<!-- 产品参数 -->
			<view class="params-section card" v-if="productDetail.params && productDetail.params.length > 0">
				<view class="section-title">
					<text class="title-text">产品参数</text>
				</view>
				<view class="params-list">
					<view class="param-item" v-for="(param, index) in productDetail.params" :key="index">
						<text class="param-name">{{ param.paramName }}</text>
						<text class="param-value">{{ param.paramValue }}</text>
					</view>
				</view>
			</view>
			
			<!-- 产品详情 -->
			<view class="content-section card" v-if="productDetail.content">
				<view class="section-title">
					<text class="title-text">产品详情</text>
				</view>
				<view class="content-html">
					<rich-text :nodes="productDetail.content"></rich-text>
				</view>
			</view>
			
			<!-- 相关产品 -->
			<view class="related-section card" v-if="relatedProducts.length > 0">
				<view class="section-title">
					<text class="title-text">相关产品</text>
				</view>
				<scroll-view class="related-list" scroll-x>
					<view class="related-item" v-for="(product, index) in relatedProducts" :key="index" @click="goToRelatedProduct(product)">
						<image class="related-image" :src="product.productImg" mode="aspectFit"></image>
						<text class="related-name">{{ product.productName }}</text>
					</view>
				</scroll-view>
			</view>
		</view>
		
		<!-- 底部操作栏 -->
		<view class="bottom-actions">
			<button class="action-btn-large collect-btn" :class="{ active: isCollected }" @click="toggleCollect">
				<text class="btn-icon">{{ isCollected ? '❤️' : '🤍' }}</text>
				<text class="btn-text">{{ isCollected ? '已收藏' : '收藏' }}</text>
			</button>
			<button class="action-btn-large share-btn" @click="shareProduct">
				<text class="btn-icon">📤</text>
				<text class="btn-text">分享</text>
			</button>
			<button class="action-btn-large download-btn" @click="downloadMaterial">
				<text class="btn-icon">📥</text>
				<text class="btn-text">下载资料</text>
			</button>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateBack, navigateTo, getStatusBarHeight, shareContent, auth } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				pageTitle: '产品详情',
				loading: false,
				productId: '',
				productDetail: {},
				relatedProducts: [],
				isCollected: false
			}
		},
		
		async onLoad(options) {
			this.statusBarHeight = await getStatusBarHeight()
			
			if (options.productId) {
				this.productId = options.productId
				this.pageTitle = options.productName || '产品详情'
				this.loadProductDetail()
			}
		},
		
		methods: {
			// 加载产品详情
			async loadProductDetail() {
				this.loading = true
				try {
					const res = await api.getProductDetail(this.productId)
					
					if (res.code === 200) {
						this.productDetail = res.data || {}
						this.pageTitle = this.productDetail.productName || '产品详情'
						
						// 检查收藏状态
						this.checkCollectStatus()
						
						// 加载相关产品
						this.loadRelatedProducts()
					}
				} catch (error) {
					console.error('加载产品详情失败:', error)
					showToast('加载失败')
				} finally {
					this.loading = false
				}
			},
			
			// 加载相关产品
			async loadRelatedProducts() {
				try {
					// 这里应该调用API获取相关产品
					// 暂时使用模拟数据
					this.relatedProducts = [
						{
							productId: '1',
							productName: '相关产品1',
							productImg: '/static/products/product1.jpg'
						},
						{
							productId: '2',
							productName: '相关产品2',
							productImg: '/static/products/product2.jpg'
						}
					]
				} catch (error) {
					console.error('加载相关产品失败:', error)
				}
			},
			
			// 检查收藏状态
			checkCollectStatus() {
				// 这里应该检查用户是否收藏了该产品
				// 暂时使用本地存储模拟
				const collectList = uni.getStorageSync('collectList') || []
				this.isCollected = collectList.includes(this.productId)
			},
			
			// 切换收藏状态
			async toggleCollect() {
				if (!auth.isLogin()) {
					showToast('请先登录')
					return
				}
				
				try {
					// 这里应该调用API切换收藏状态
					// 暂时使用本地存储模拟
					let collectList = uni.getStorageSync('collectList') || []
					
					if (this.isCollected) {
						// 取消收藏
						collectList = collectList.filter(id => id !== this.productId)
						showToast('已取消收藏', 'success')
					} else {
						// 添加收藏
						collectList.push(this.productId)
						showToast('收藏成功', 'success')
					}
					
					uni.setStorageSync('collectList', collectList)
					this.isCollected = !this.isCollected
				} catch (error) {
					console.error('收藏操作失败:', error)
					showToast('操作失败')
				}
			},
			
			// 分享产品
			async shareProduct() {
				try {
					const shareData = {
						title: this.productDetail.productName,
						path: `/pages/detail/detail?productId=${this.productId}`,
						imageUrl: this.productDetail.banners?.[0]?.imageUrl || ''
					}
					
					await shareContent(shareData.title, shareData.path, shareData.imageUrl)
				} catch (error) {
					console.error('分享失败:', error)
				}
			},
			
			// 下载资料
			downloadMaterial() {
				navigateTo('/pages/download/download', {
					productId: this.productId,
					productName: this.productDetail.productName
				})
			},
			
			// 预览图片
			previewImage(imageUrl) {
				const urls = this.productDetail.banners?.map(banner => banner.imageUrl) || [imageUrl]
				uni.previewImage({
					urls,
					current: imageUrl
				})
			},
			
			// 跳转到相关产品
			goToRelatedProduct(product) {
				navigateTo('/pages/detail/detail', {
					productId: product.productId,
					productName: product.productName
				})
			},
			
			// 返回
			goBack() {
				navigateBack()
			}
		}
	}
</script>

<style scoped>
	.detail-page {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding-bottom: 120rpx;
	}
	
	/* 导航栏 */
	.navbar-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		height: 88rpx;
	}
	
	.back-btn {
		font-size: 36rpx;
		color: #333;
		padding: 10rpx;
	}
	
	.navbar-title {
		flex: 1;
		text-align: center;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin: 0 20rpx;
	}
	
	.navbar-actions {
		display: flex;
		gap: 15rpx;
	}
	
	.action-btn {
		font-size: 32rpx;
		padding: 10rpx;
	}
	
	/* 轮播图 */
	.banner-section {
		margin-bottom: 20rpx;
	}
	
	.banner-swiper {
		height: 400rpx;
	}
	
	.banner-image {
		width: 100%;
		height: 100%;
	}
	
	/* 产品信息 */
	.product-info {
		margin: 20rpx;
		padding: 30rpx;
	}
	
	.product-name {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 15rpx;
		line-height: 1.4;
	}
	
	.product-code {
		display: block;
		font-size: 26rpx;
		color: #666;
		margin-bottom: 15rpx;
	}
	
	.product-desc {
		display: block;
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 20rpx;
	}
	
	.product-tags {
		display: flex;
		flex-wrap: wrap;
		gap: 10rpx;
	}
	
	.tag {
		display: inline-block;
		padding: 10rpx 20rpx;
		background-color: #f0f8ff;
		color: #007aff;
		border-radius: 20rpx;
		font-size: 24rpx;
	}
	
	/* 参数列表 */
	.params-section, .content-section, .related-section {
		margin: 20rpx;
		padding: 30rpx;
	}
	
	.section-title {
		margin-bottom: 20rpx;
	}
	
	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.params-list {
		border-top: 1rpx solid #f0f0f0;
	}
	
	.param-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 25rpx 0;
		border-bottom: 1rpx solid #f8f8f8;
	}
	
	.param-name {
		font-size: 28rpx;
		color: #666;
	}
	
	.param-value {
		font-size: 28rpx;
		color: #333;
		font-weight: 500;
	}
	
	/* 内容区域 */
	.content-html {
		font-size: 28rpx;
		line-height: 1.8;
		color: #333;
	}
	
	/* 相关产品 */
	.related-list {
		white-space: nowrap;
	}
	
	.related-item {
		display: inline-block;
		width: 200rpx;
		margin-right: 20rpx;
		text-align: center;
		vertical-align: top;
	}
	
	.related-image {
		width: 100%;
		height: 150rpx;
		border-radius: 10rpx;
		margin-bottom: 15rpx;
	}
	
	.related-name {
		display: block;
		font-size: 24rpx;
		color: #333;
		line-height: 1.4;
	}
	
	/* 底部操作栏 */
	.bottom-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		background-color: white;
		border-top: 1rpx solid #f0f0f0;
		padding: 20rpx;
		gap: 15rpx;
	}
	
	.action-btn-large {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 20rpx;
		border-radius: 15rpx;
		border: none;
		font-size: 24rpx;
	}
	
	.collect-btn {
		background-color: #f8f8f8;
		color: #666;
	}
	
	.collect-btn.active {
		background-color: #ffe6e6;
		color: #ff4757;
	}
	
	.share-btn {
		background-color: #e6f3ff;
		color: #007aff;
	}
	
	.download-btn {
		background-color: #e6ffe6;
		color: #28a745;
	}
	
	.btn-icon {
		font-size: 32rpx;
		margin-bottom: 8rpx;
	}
	
	.btn-text {
		font-size: 22rpx;
	}
	
	/* 加载状态 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}
	
	.loading-content {
		text-align: center;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #007aff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
	}
</style>
