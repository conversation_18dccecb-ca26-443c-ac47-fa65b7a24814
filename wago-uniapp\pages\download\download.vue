<template>
	<view class="download-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">下载资料</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- Tab切换 -->
			<view class="tab-container">
				<view class="tab-item" 
					:class="{ active: activeTab === 0 }" 
					@click="switchTab(0)">
					<text class="tab-text">认证证书</text>
				</view>
				<view class="tab-item" 
					:class="{ active: activeTab === 1 }" 
					@click="switchTab(1)">
					<text class="tab-text">产品手册</text>
				</view>
				<view class="tab-indicator" :style="{ left: activeTab * 50 + '%' }"></view>
			</view>
			
			<!-- 筛选栏 -->
			<view class="filter-section">
				<view class="filter-row">
					<view class="filter-item" @click="showTypeFilter">
						<text class="filter-label">类型</text>
						<text class="filter-value">{{ selectedType.name || '全部' }}</text>
						<text class="filter-arrow">▼</text>
					</view>
					<view class="filter-item" @click="showLanguageFilter">
						<text class="filter-label">语言</text>
						<text class="filter-value">{{ selectedLanguage.name || '全部' }}</text>
						<text class="filter-arrow">▼</text>
					</view>
				</view>
			</view>
			
			<!-- 内容列表 -->
			<scroll-view class="content-list" scroll-y @scrolltolower="loadMore" 
				:refresher-enabled="true" :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
				
				<!-- 认证证书列表 -->
				<view v-if="activeTab === 0">
					<view class="certificate-item" v-for="(item, index) in certificates" :key="index">
						<view class="item-header">
							<image class="item-icon" src="/static/icons/certificate.png" mode="aspectFit"></image>
							<view class="item-info">
								<text class="item-title">{{ item.certificateName }}</text>
								<text class="item-desc">{{ item.certificateDesc }}</text>
								<view class="item-meta">
									<text class="meta-text">类型: {{ item.certificateType }}</text>
									<text class="meta-text">语言: {{ item.language }}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
							<button class="btn-preview" @click="previewFile(item)">预览</button>
							<button class="btn-download" @click="downloadFile(item)">下载</button>
						</view>
					</view>
				</view>
				
				<!-- 产品手册列表 -->
				<view v-if="activeTab === 1">
					<view class="manual-item" v-for="(item, index) in manuals" :key="index">
						<view class="item-header">
							<image class="item-icon" src="/static/icons/manual.png" mode="aspectFit"></image>
							<view class="item-info">
								<text class="item-title">{{ item.manualName }}</text>
								<text class="item-desc">{{ item.manualDesc }}</text>
								<view class="item-meta">
									<text class="meta-text">类型: {{ item.manualType }}</text>
									<text class="meta-text">语言: {{ item.language }}</text>
									<text class="meta-text">大小: {{ item.fileSize }}</text>
								</view>
							</view>
						</view>
						<view class="item-actions">
							<button class="btn-preview" @click="previewFile(item)">预览</button>
							<button class="btn-download" @click="downloadFile(item)">下载</button>
						</view>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<text v-if="loadingMore">加载中...</text>
					<text v-else>上拉加载更多</text>
				</view>
				
				<!-- 没有更多数据 -->
				<view class="no-more" v-if="!hasMore && getCurrentList().length > 0">
					<text>没有更多数据了</text>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="getCurrentList().length === 0 && !loading">
					<image class="empty-icon" src="/static/icons/empty.png" mode="aspectFit"></image>
					<text class="empty-text">暂无{{ activeTab === 0 ? '证书' : '手册' }}数据</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 筛选弹窗 -->
		<view class="filter-popup-mask" v-if="showFilterPopup" @click="closeFilter">
			<view class="filter-popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">{{ currentFilterTitle }}</text>
					<text class="popup-close" @click="closeFilter">✕</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" 
						v-for="(option, index) in currentFilterOptions" 
						:key="index"
						:class="{ active: option.selected }"
						@click="selectFilterOption(option)">
						<text class="option-text">{{ option.name }}</text>
						<text class="option-check" v-if="option.selected">✓</text>
					</view>
				</view>
				<view class="popup-actions">
					<button class="btn-reset" @click="resetFilter">重置</button>
					<button class="btn-confirm" @click="confirmFilter">确定</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, showLoading, hideLoading, navigateTo, getStatusBarHeight, downloadFile } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				activeTab: 0, // 0: 认证证书, 1: 产品手册
				
				// 数据
				certificates: [],
				manuals: [],
				loading: false,
				refreshing: false,
				loadingMore: false,
				hasMore: true,
				page: 1,
				pageSize: 10,
				
				// 筛选
				selectedType: {},
				selectedLanguage: {},
				typeOptions: [],
				languageOptions: [],
				
				// 筛选弹窗
				showFilterPopup: false,
				currentFilterType: '',
				currentFilterTitle: '',
				currentFilterOptions: []
			}
		},
		
		async onLoad() {
			this.statusBarHeight = await getStatusBarHeight()
			this.loadFilterOptions()
			this.loadData()
		},
		
		methods: {
			// 切换Tab
			switchTab(index) {
				if (this.activeTab !== index) {
					this.activeTab = index
					this.resetFilters()
					this.loadData(true)
				}
			},
			
			// 获取当前列表
			getCurrentList() {
				return this.activeTab === 0 ? this.certificates : this.manuals
			},
			
			// 加载筛选选项
			async loadFilterOptions() {
				try {
					// 模拟数据，实际应该从API获取
					this.typeOptions = [
						{ id: '', name: '全部类型', selected: false },
						{ id: '1', name: 'CE认证', selected: false },
						{ id: '2', name: 'UL认证', selected: false },
						{ id: '3', name: 'FCC认证', selected: false }
					]
					
					this.languageOptions = [
						{ id: '', name: '全部语言', selected: false },
						{ id: 'zh', name: '中文', selected: false },
						{ id: 'en', name: 'English', selected: false },
						{ id: 'de', name: 'Deutsch', selected: false }
					]
				} catch (error) {
					console.error('加载筛选选项失败:', error)
				}
			},
			
			// 加载数据
			async loadData(reset = false) {
				if (reset) {
					this.page = 1
					this.hasMore = true
					if (this.activeTab === 0) {
						this.certificates = []
					} else {
						this.manuals = []
					}
				}
				
				if (this.loading || this.loadingMore) return
				
				if (reset) {
					this.loading = true
				} else {
					this.loadingMore = true
				}
				
				try {
					const params = {
						page: this.page,
						pageSize: this.pageSize,
						type: this.selectedType.id || '',
						language: this.selectedLanguage.id || ''
					}
					
					let res
					if (this.activeTab === 0) {
						res = await api.getCertificates(params)
					} else {
						res = await api.getManuals(params)
					}
					
					if (res.code === 200) {
						const newData = res.data || []
						
						if (reset) {
							if (this.activeTab === 0) {
								this.certificates = newData
							} else {
								this.manuals = newData
							}
						} else {
							if (this.activeTab === 0) {
								this.certificates.push(...newData)
							} else {
								this.manuals.push(...newData)
							}
						}
						
						this.hasMore = newData.length === this.pageSize
						this.page++
					}
				} catch (error) {
					console.error('加载数据失败:', error)
					showToast('加载数据失败')
				} finally {
					this.loading = false
					this.loadingMore = false
					this.refreshing = false
				}
			},
			
			// 下拉刷新
			onRefresh() {
				this.refreshing = true
				this.loadData(true)
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loadingMore) {
					this.loadData()
				}
			},
			
			// 重置筛选
			resetFilters() {
				this.selectedType = {}
				this.selectedLanguage = {}
			},
			
			// 显示类型筛选
			showTypeFilter() {
				this.currentFilterType = 'type'
				this.currentFilterTitle = '选择类型'
				this.currentFilterOptions = [...this.typeOptions]
				this.showFilterPopup = true
			},
			
			// 显示语言筛选
			showLanguageFilter() {
				this.currentFilterType = 'language'
				this.currentFilterTitle = '选择语言'
				this.currentFilterOptions = [...this.languageOptions]
				this.showFilterPopup = true
			},
			
			// 选择筛选选项
			selectFilterOption(option) {
				this.currentFilterOptions.forEach(item => {
					item.selected = item.id === option.id
				})
			},
			
			// 重置筛选
			resetFilter() {
				this.currentFilterOptions.forEach(item => {
					item.selected = item.id === ''
				})
			},
			
			// 确认筛选
			confirmFilter() {
				const selected = this.currentFilterOptions.find(item => item.selected) || this.currentFilterOptions[0]
				
				if (this.currentFilterType === 'type') {
					this.selectedType = selected
					this.typeOptions = [...this.currentFilterOptions]
				} else if (this.currentFilterType === 'language') {
					this.selectedLanguage = selected
					this.languageOptions = [...this.currentFilterOptions]
				}
				
				this.closeFilter()
				this.loadData(true)
			},
			
			// 关闭筛选弹窗
			closeFilter() {
				this.showFilterPopup = false
			},
			
			// 预览文件
			previewFile(item) {
				if (item.fileUrl) {
					navigateTo('/pages/preview/preview', {
						url: item.fileUrl,
						title: item.certificateName || item.manualName
					})
				} else {
					showToast('暂无预览文件')
				}
			},
			
			// 下载文件
			async downloadFile(item) {
				if (!item.downloadUrl) {
					showToast('暂无下载链接')
					return
				}
				
				try {
					showLoading('下载中...')
					await downloadFile(item.downloadUrl, item.certificateName || item.manualName)
				} catch (error) {
					console.error('下载失败:', error)
					showToast('下载失败')
				} finally {
					hideLoading()
				}
			}
		}
	}
</script>

<style scoped>
	.download-page {
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* Tab切换 */
	.tab-container {
		position: relative;
		display: flex;
		background-color: white;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 30rpx 0;
		position: relative;
	}
	
	.tab-text {
		font-size: 30rpx;
		color: #666;
		transition: color 0.3s;
	}
	
	.tab-item.active .tab-text {
		color: #007aff;
		font-weight: bold;
	}
	
	.tab-indicator {
		position: absolute;
		bottom: 0;
		width: 50%;
		height: 4rpx;
		background-color: #007aff;
		transition: left 0.3s;
	}
	
	/* 筛选区域 */
	.filter-section {
		background-color: white;
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.filter-row {
		display: flex;
		gap: 20rpx;
	}
	
	.filter-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
	}
	
	.filter-label {
		font-size: 26rpx;
		color: #666;
		margin-right: 10rpx;
	}
	
	.filter-value {
		flex: 1;
		font-size: 26rpx;
		color: #333;
		text-align: center;
	}
	
	.filter-arrow {
		font-size: 20rpx;
		color: #999;
	}
	
	/* 内容列表 */
	.content-list {
		height: calc(100vh - 280rpx);
		padding: 20rpx;
	}
	
	.certificate-item, .manual-item {
		background-color: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.item-header {
		display: flex;
		margin-bottom: 20rpx;
	}
	
	.item-icon {
		width: 80rpx;
		height: 80rpx;
		margin-right: 20rpx;
	}
	
	.item-info {
		flex: 1;
	}
	
	.item-title {
		display: block;
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		line-height: 1.4;
	}
	
	.item-desc {
		display: block;
		font-size: 26rpx;
		color: #666;
		line-height: 1.4;
		margin-bottom: 15rpx;
	}
	
	.item-meta {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
	}
	
	.meta-text {
		font-size: 24rpx;
		color: #999;
	}
	
	.item-actions {
		display: flex;
		gap: 20rpx;
	}
	
	.btn-preview, .btn-download {
		flex: 1;
		padding: 20rpx;
		border-radius: 10rpx;
		text-align: center;
		font-size: 26rpx;
		border: none;
	}
	
	.btn-preview {
		background-color: #f8f8f8;
		color: #666;
	}
	
	.btn-download {
		background-color: #007aff;
		color: white;
	}
	
	/* 加载状态 */
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}
	
	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 100rpx 20rpx;
	}
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		font-size: 28rpx;
		color: #999;
	}
	
	/* 筛选弹窗样式复用 */
	.filter-popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		display: flex;
		align-items: flex-end;
	}
	
	.filter-popup-content {
		width: 100%;
		background-color: white;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
	}
	
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-close {
		font-size: 32rpx;
		color: #999;
		padding: 10rpx;
	}
	
	.filter-options {
		max-height: 60vh;
		overflow-y: auto;
	}
	
	.filter-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f8f8f8;
	}
	
	.filter-option.active {
		background-color: #f0f8ff;
	}
	
	.option-text {
		font-size: 30rpx;
		color: #333;
	}
	
	.filter-option.active .option-text {
		color: #007aff;
	}
	
	.option-check {
		font-size: 32rpx;
		color: #007aff;
	}
	
	.popup-actions {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
	}
	
	.btn-reset, .btn-confirm {
		flex: 1;
		padding: 25rpx;
		border-radius: 25rpx;
		text-align: center;
		font-size: 30rpx;
		border: none;
	}
	
	.btn-reset {
		background-color: #f8f8f8;
		color: #666;
	}
	
	.btn-confirm {
		background-color: #007aff;
		color: white;
	}
</style>
