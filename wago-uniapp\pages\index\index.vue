<template>
	<view class="index-page">
		<!-- 搜索栏 -->
		<view class="search-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="search-container">
				<view class="search-box" @click="goToSearch">
					<image class="search-icon" src="/static/icons/search.png" mode="aspectFit"></image>
					<text class="search-text">Search</text>
				</view>
			</view>
		</view>

		<!-- 内容区域 -->
		<scroll-view class="content-scroll" scroll-y refresher-enabled :refresher-triggered="refreshing" @refresherrefresh="onRefresh">
			<!-- 用户信息区域 -->
			<view class="user-section">
				<view class="welcome-text">
					<text class="welcome-title">欢迎进入</text>
					<text class="welcome-subtitle">WAGO Product Catalogue</text>
				</view>
				<view class="user-center" @click="goToUserCenter">
					<image class="user-avatar" :src="userInfo.headImgUrl || '/static/icons/default_avatar.png'" mode="aspectFill"></image>
					<text class="user-center-text">个人中心</text>
				</view>
			</view>

			<!-- 轮播图 -->
			<view class="banner-section" v-if="banners.length > 0">
				<swiper class="banner-swiper" indicator-dots autoplay circular>
					<swiper-item v-for="(banner, index) in banners" :key="index">
						<image class="banner-image" :src="banner.imgUrl" mode="aspectFill" @click="onBannerClick(banner)"></image>
					</swiper-item>
				</swiper>
			</view>

			<!-- 产品分类标题 -->
			<view class="section-title">
				<text class="title-text">产品分类</text>
			</view>

			<!-- 产品分类网格 -->
			<view class="product-grid">
				<view class="product-item"
					v-for="(product, index) in products"
					:key="index"
					:class="{ 'item-left': index % 2 === 0, 'item-right': index % 2 === 1 }"
					@click="goToProductDetail(product)">
					<view class="product-card">
						<view class="product-image-container">
							<image class="product-image" :src="product.imgurl || product.imgUrl" mode="aspectFit"></image>
						</view>
						<view class="product-info">
							<text class="product-name">{{ product.productCategoryName || product.name }}</text>
							<image class="arrow-icon" src="/static/icons/arrow_more.png" mode="aspectFit"></image>
						</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="products.length === 0 && !loading">
				<text class="empty-text">暂无产品数据</text>
			</view>
		</scroll-view>

		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateTo, getStatusBarHeight, auth } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				isLogin: false,
				userInfo: {},
				banners: [],
				products: [],
				loading: false,
				refreshing: false
			}
		},
		
		async onLoad() {
			// 获取状态栏高度
			this.statusBarHeight = await getStatusBarHeight()
			
			// 检查登录状态
			this.checkLoginStatus()
			
			// 加载数据
			this.loadData()
		},
		
		onShow() {
			// 每次显示页面时检查登录状态
			this.checkLoginStatus()
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.loadData().then(() => {
				uni.stopPullDownRefresh()
			})
		},
		
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				this.isLogin = auth.isLogin()
				this.userInfo = auth.getUserInfo()
			},

			// 加载数据
			async loadData() {
				this.loading = true
				try {
					// 先加载轮播图
					await this.loadBanners()
					// 再加载产品分类
					await this.loadProducts()
				} catch (error) {
					console.error('加载数据失败:', error)
					showToast('加载数据失败')
				} finally {
					this.loading = false
					this.refreshing = false
				}
			},

			// 加载轮播图
			async loadBanners() {
				try {
					const res = await api.getBanners()
					if (res.code === 200) {
						this.banners = res.data || []
					}
				} catch (error) {
					console.error('加载轮播图失败:', error)
				}
			},

			// 加载产品分类
			async loadProducts() {
				try {
					const res = await api.getProductCategories()
					if (res.code === 200) {
						this.products = res.data || []
						// 加载首页计划
						await this.loadShouPlan()
					}
				} catch (error) {
					console.error('加载产品分类失败:', error)
				}
			},

			// 加载首页计划
			async loadShouPlan() {
				try {
					const res = await api.getShouPlan()
					if (res.code === 200 && res.data) {
						// 添加到产品列表
						this.products.push({
							productPlanCategoryId: res.data.productPlanCategoryId,
							imgUrl: res.data.imgUrl,
							name: res.data.name,
							isProduct: false
						})
					}
				} catch (error) {
					console.error('加载首页计划失败:', error)
				}
			},
			
			// 轮播图点击
			onBannerClick(banner) {
				if (banner.linkUrl) {
					// 如果有链接，跳转到对应页面
					navigateTo('/pages/detail/detail', { 
						type: 'banner',
						id: banner.bannerId,
						url: banner.linkUrl
					})
				}
			},
			
			// 跳转到搜索页面
			goToSearch() {
				navigateTo('/pages/search/search')
			},
			
			// 跳转到登录页面
			goToLogin() {
				navigateTo('/pages/login/login')
			},
			
			// 下拉刷新
			onRefresh() {
				this.refreshing = true
				this.loadData()
			},

			// 跳转到用户中心
			goToUserCenter() {
				if (!this.isLogin) {
					navigateTo('/pages/login/login')
				} else {
					navigateTo('/pages/about/about')
				}
			},

			// 跳转到产品详情
			goToProductDetail(product) {
				const isProduct = product.productCategoryId !== undefined
				if (isProduct) {
					navigateTo('/pages/product/product', {
						categoryId: product.productCategoryId,
						categoryName: product.productCategoryName
					})
				} else {
					navigateTo('/pages/detail/detail', {
						type: 'plan',
						productId: product.productPlanCategoryId,
						productName: product.name
					})
				}
			}
		}
	}
</script>

<style scoped>
	.index-page {
		min-height: 100vh;
		background-color: #F2F2F2;
	}

	/* 搜索头部 */
	.search-header {
		background-color: #F2F2F2;
		padding: 30rpx 40rpx;
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.search-container {
		/* padding: 0; */
	}

	.search-box {
		display: flex;
		align-items: center;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 24rpx 30rpx;
		height: 90rpx;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
	}

	.search-icon {
		width: 36rpx;
		height: 36rpx;
		margin-right: 24rpx;
	}

	.search-text {
		font-size: 30rpx;
		color: #A4A8B0;
	}

	/* 内容滚动区域 */
	.content-scroll {
		height: calc(100vh - 120rpx);
	}

	/* 用户信息区域 */
	.user-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 40rpx;
		background-color: #F2F2F2;
	}

	.welcome-text {
		flex: 1;
	}

	.welcome-title {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #75B727;
		line-height: 1.4;
	}

	.welcome-subtitle {
		display: block;
		font-size: 36rpx;
		font-weight: bold;
		color: #75B727;
		line-height: 1.4;
	}

	.user-center {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx;
	}

	.user-avatar {
		width: 100rpx;
		height: 100rpx;
		border-radius: 50%;
		margin-bottom: 16rpx;
		background-color: #ffffff;
		border: 4rpx solid #e0e0e0;
	}

	.user-center-text {
		font-size: 26rpx;
		color: #75B727;
		font-weight: 500;
	}

	/* 轮播图 */
	.banner-section {
		margin: 30rpx 40rpx;
	}

	.banner-swiper {
		height: 320rpx;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(117, 183, 39, 0.3);
	}

	.banner-image {
		width: 100%;
		height: 100%;
	}

	/* 分类标题 */
	.section-title {
		padding: 30rpx 40rpx 20rpx;
		background-color: #F2F2F2;
	}

	.title-text {
		font-size: 36rpx;
		font-weight: bold;
		color: #323232;
	}

	/* 产品网格 */
	.product-grid {
		padding: 0 40rpx 80rpx;
		background-color: #F2F2F2;
		display: flex;
		flex-wrap: wrap;
		margin: -16rpx;
	}

	.product-item {
		width: 50%;
		padding: 16rpx;
		box-sizing: border-box;
	}

	.item-left {
		/* float: left; */
	}

	.item-right {
		/* float: right; */
	}

	.product-card {
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx 30rpx;
		text-align: center;
		position: relative;
		box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.1);
		border: 2rpx solid #e8e8e8;
	}

	.product-image-container {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 30rpx;
	}

	.product-image {
		max-width: 100rpx;
		max-height: 100rpx;
	}

	.product-info {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
	}

	.product-name {
		font-size: 28rpx;
		color: #323232;
		font-weight: 500;
		flex: 1;
		text-align: left;
		line-height: 1.3;
	}

	.arrow-icon {
		width: 32rpx;
		height: 32rpx;
	}

	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 100rpx 20rpx;
		background-color: #ffffff;
	}

	.empty-text {
		font-size: 28rpx;
		color: #999999;
	}

	/* 加载状态 */
	.loading-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}

	.loading-content {
		text-align: center;
	}

	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #007aff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		font-size: 28rpx;
		color: #666666;
	}
</style>
