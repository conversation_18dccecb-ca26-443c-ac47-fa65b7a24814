<template>
	<view class="index-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">欢迎进入WAGO Product Catalogue</view>
				<view class="search-icon" @click="goToSearch">
					<text class="iconfont icon-search">🔍</text>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 用户信息区域 -->
			<view class="user-section card">
				<view class="user-info" v-if="isLogin">
					<view class="user-avatar">
						<text class="avatar-text">{{ userInfo.name ? userInfo.name.charAt(0) : 'U' }}</text>
					</view>
					<view class="user-details">
						<text class="user-name">{{ userInfo.name || '用户' }}</text>
						<text class="user-desc">欢迎使用WAGO产品查询</text>
					</view>
				</view>
				<view class="guest-info" v-else>
					<view class="guest-avatar">
						<text class="avatar-text">游</text>
					</view>
					<view class="guest-details">
						<text class="guest-name">游客用户</text>
						<text class="guest-desc" @click="goToLogin">点击登录获得更好体验</text>
					</view>
				</view>
			</view>
			
			<!-- 轮播图 -->
			<view class="banner-section" v-if="banners.length > 0">
				<swiper class="banner-swiper" indicator-dots autoplay circular>
					<swiper-item v-for="(banner, index) in banners" :key="index">
						<image class="banner-image" :src="banner.bannerUrl" mode="aspectFill" @click="onBannerClick(banner)"></image>
					</swiper-item>
				</swiper>
			</view>
			
			<!-- 产品分类标题 -->
			<view class="section-title">
				<text class="title-text">产品分类</text>
			</view>
			
			<!-- 产品分类网格 -->
			<view class="product-grid">
				<view class="product-item" v-for="(product, index) in products" :key="index" @click="goToProductDetail(product)">
					<view class="product-image-container">
						<image class="product-image" :src="product.productImg" mode="aspectFit"></image>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.productName }}</text>
						<text class="product-desc">{{ product.productDesc || '点击查看详情' }}</text>
					</view>
				</view>
			</view>
			
			<!-- 空状态 -->
			<view class="empty-state" v-if="products.length === 0 && !loading">
				<text class="empty-text">暂无产品数据</text>
			</view>
			
			<!-- 加载状态 -->
			<view class="loading" v-if="loading">
				<text>加载中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateTo, getStatusBarHeight, auth } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				isLogin: false,
				userInfo: {},
				banners: [],
				products: [],
				loading: false
			}
		},
		
		async onLoad() {
			// 获取状态栏高度
			this.statusBarHeight = await getStatusBarHeight()
			
			// 检查登录状态
			this.checkLoginStatus()
			
			// 加载数据
			this.loadData()
		},
		
		onShow() {
			// 每次显示页面时检查登录状态
			this.checkLoginStatus()
		},
		
		// 下拉刷新
		onPullDownRefresh() {
			this.loadData().then(() => {
				uni.stopPullDownRefresh()
			})
		},
		
		methods: {
			// 检查登录状态
			checkLoginStatus() {
				this.isLogin = auth.isLogin()
				this.userInfo = auth.getUserInfo()
			},
			
			// 加载数据
			async loadData() {
				this.loading = true
				try {
					// 并行加载轮播图和产品分类
					const [bannersRes, productsRes] = await Promise.all([
						api.getBanners(),
						api.getProductCategories()
					])
					
					if (bannersRes.code === 200) {
						this.banners = bannersRes.data || []
					}
					
					if (productsRes.code === 200) {
						this.products = productsRes.data || []
					}
				} catch (error) {
					console.error('加载数据失败:', error)
					showToast('加载数据失败')
				} finally {
					this.loading = false
				}
			},
			
			// 轮播图点击
			onBannerClick(banner) {
				if (banner.linkUrl) {
					// 如果有链接，跳转到对应页面
					navigateTo('/pages/detail/detail', { 
						type: 'banner',
						id: banner.bannerId,
						url: banner.linkUrl
					})
				}
			},
			
			// 跳转到搜索页面
			goToSearch() {
				navigateTo('/pages/search/search')
			},
			
			// 跳转到登录页面
			goToLogin() {
				navigateTo('/pages/login/login')
			},
			
			// 跳转到产品详情
			goToProductDetail(product) {
				navigateTo('/pages/detail/detail', {
					type: 'product',
					productId: product.productId,
					productName: product.productName
				})
			}
		}
	}
</script>

<style scoped>
	.index-page {
		min-height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 用户信息区域 */
	.user-section {
		margin: 20rpx;
		padding: 30rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border-radius: 20rpx;
	}
	
	.user-info, .guest-info {
		display: flex;
		align-items: center;
	}
	
	.user-avatar, .guest-avatar {
		width: 80rpx;
		height: 80rpx;
		border-radius: 50%;
		background-color: rgba(255, 255, 255, 0.3);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 20rpx;
	}
	
	.avatar-text {
		font-size: 32rpx;
		font-weight: bold;
		color: white;
	}
	
	.user-details, .guest-details {
		flex: 1;
	}
	
	.user-name, .guest-name {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 10rpx;
	}
	
	.user-desc, .guest-desc {
		display: block;
		font-size: 24rpx;
		opacity: 0.8;
	}
	
	.guest-desc {
		text-decoration: underline;
	}
	
	/* 轮播图 */
	.banner-section {
		margin: 20rpx;
	}
	
	.banner-swiper {
		height: 300rpx;
		border-radius: 20rpx;
		overflow: hidden;
	}
	
	.banner-image {
		width: 100%;
		height: 100%;
	}
	
	/* 分类标题 */
	.section-title {
		padding: 30rpx 20rpx 20rpx;
	}
	
	.title-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	/* 产品网格 */
	.product-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
		padding: 0 20rpx 40rpx;
	}
	
	.product-item {
		background-color: white;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
		transition: transform 0.2s;
	}
	
	.product-item:active {
		transform: scale(0.95);
	}
	
	.product-image-container {
		width: 100%;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;
	}
	
	.product-image {
		max-width: 100%;
		max-height: 100%;
	}
	
	.product-info {
		text-align: center;
	}
	
	.product-name {
		display: block;
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		line-height: 1.4;
	}
	
	.product-desc {
		display: block;
		font-size: 24rpx;
		color: #666;
		line-height: 1.3;
	}
	
	/* 搜索图标 */
	.search-icon {
		padding: 10rpx;
		font-size: 32rpx;
	}
</style>
