<template>
	<view class="login-page">
		<!-- 背景 -->
		<view class="background">
			<image class="bg-image" src="/static/bg/login-bg.jpg" mode="aspectFill"></image>
			<view class="bg-overlay"></view>
		</view>
		
		<!-- 内容区域 -->
		<view class="content">
			<!-- Logo区域 -->
			<view class="logo-section">
				<image class="logo" src="/static/logo.png" mode="aspectFit"></image>
				<text class="app-name">WAGO产品查询</text>
				<text class="app-desc">连接电气工程技术</text>
			</view>
			
			<!-- 登录表单 -->
			<view class="form-section">
				<view class="form-container">
					<!-- Tab切换 -->
					<view class="tab-container">
						<view class="tab-item" 
							:class="{ active: loginType === 'password' }" 
							@click="switchLoginType('password')">
							<text class="tab-text">密码登录</text>
						</view>
						<view class="tab-item" 
							:class="{ active: loginType === 'code' }" 
							@click="switchLoginType('code')">
							<text class="tab-text">验证码登录</text>
						</view>
					</view>
					
					<!-- 密码登录 -->
					<view class="form-content" v-if="loginType === 'password'">
						<view class="input-group">
							<view class="input-item">
								<text class="input-icon">👤</text>
								<input class="input-field" 
									type="text" 
									placeholder="请输入用户名" 
									v-model="form.username"
									maxlength="50" />
							</view>
							<view class="input-item">
								<text class="input-icon">🔒</text>
								<input class="input-field" 
									:type="showPassword ? 'text' : 'password'" 
									placeholder="请输入密码" 
									v-model="form.password"
									maxlength="20" />
								<text class="input-toggle" @click="togglePassword">
									{{ showPassword ? '👁️' : '👁️‍🗨️' }}
								</text>
							</view>
						</view>
						
						<view class="form-options">
							<text class="forgot-password" @click="goToForgotPassword">忘记密码？</text>
						</view>
						
						<button class="login-btn" @click="loginWithPassword" :disabled="!canLogin">
							{{ loading ? '登录中...' : '登录' }}
						</button>
					</view>
					
					<!-- 验证码登录 -->
					<view class="form-content" v-if="loginType === 'code'">
						<view class="input-group">
							<view class="input-item">
								<text class="input-icon">📱</text>
								<input class="input-field" 
									type="number" 
									placeholder="请输入手机号" 
									v-model="form.phone"
									maxlength="11" />
							</view>
							<view class="input-item">
								<text class="input-icon">🔢</text>
								<input class="input-field" 
									type="number" 
									placeholder="请输入验证码" 
									v-model="form.code"
									maxlength="6" />
								<button class="code-btn" 
									@click="sendCode" 
									:disabled="!canSendCode || codeCountdown > 0">
									{{ codeCountdown > 0 ? `${codeCountdown}s` : '获取验证码' }}
								</button>
							</view>
						</view>
						
						<button class="login-btn" @click="loginWithCode" :disabled="!canLoginWithCode">
							{{ loading ? '登录中...' : '登录' }}
						</button>
					</view>
					
					<!-- 其他选项 -->
					<view class="other-options">
						<button class="guest-btn" @click="guestLogin">游客访问</button>
						<text class="register-link" @click="goToRegister">还没有账号？立即注册</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateTo, switchTab, auth } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				loginType: 'password', // password | code
				showPassword: false,
				loading: false,
				codeCountdown: 0,
				
				form: {
					username: '',
					password: '',
					phone: '',
					code: ''
				}
			}
		},
		
		computed: {
			// 是否可以登录（密码方式）
			canLogin() {
				return this.form.username.trim() && this.form.password.trim() && !this.loading
			},
			
			// 是否可以发送验证码
			canSendCode() {
				return /^1[3-9]\d{9}$/.test(this.form.phone)
			},
			
			// 是否可以登录（验证码方式）
			canLoginWithCode() {
				return this.canSendCode && this.form.code.length === 6 && !this.loading
			}
		},
		
		onLoad() {
			// 检查是否已经登录
			if (auth.isLogin()) {
				switchTab('/pages/index/index')
			}
		},
		
		methods: {
			// 切换登录方式
			switchLoginType(type) {
				this.loginType = type
				this.clearForm()
			},
			
			// 清空表单
			clearForm() {
				this.form = {
					username: '',
					password: '',
					phone: '',
					code: ''
				}
			},
			
			// 切换密码显示
			togglePassword() {
				this.showPassword = !this.showPassword
			},
			
			// 密码登录
			async loginWithPassword() {
				if (!this.canLogin) return
				
				this.loading = true
				try {
					const res = await api.login({
						username: this.form.username,
						password: this.form.password,
						loginType: 'password'
					})
					
					if (res.code === 200) {
						// 保存登录信息
						auth.setLogin(res.data.token, res.data.userInfo)
						showToast('登录成功', 'success')
						
						// 跳转到首页
						setTimeout(() => {
							switchTab('/pages/index/index')
						}, 1000)
					} else {
						showToast(res.message || '登录失败')
					}
				} catch (error) {
					console.error('登录失败:', error)
					showToast('登录失败，请检查网络连接')
				} finally {
					this.loading = false
				}
			},
			
			// 发送验证码
			async sendCode() {
				if (!this.canSendCode || this.codeCountdown > 0) return
				
				try {
					const res = await api.sendCode({
						phone: this.form.phone
					})
					
					if (res.code === 200) {
						showToast('验证码已发送', 'success')
						this.startCountdown()
					} else {
						showToast(res.message || '发送失败')
					}
				} catch (error) {
					console.error('发送验证码失败:', error)
					showToast('发送失败，请稍后重试')
				}
			},
			
			// 开始倒计时
			startCountdown() {
				this.codeCountdown = 60
				const timer = setInterval(() => {
					this.codeCountdown--
					if (this.codeCountdown <= 0) {
						clearInterval(timer)
					}
				}, 1000)
			},
			
			// 验证码登录
			async loginWithCode() {
				if (!this.canLoginWithCode) return
				
				this.loading = true
				try {
					const res = await api.login({
						phone: this.form.phone,
						code: this.form.code,
						loginType: 'code'
					})
					
					if (res.code === 200) {
						auth.setLogin(res.data.token, res.data.userInfo)
						showToast('登录成功', 'success')
						
						setTimeout(() => {
							switchTab('/pages/index/index')
						}, 1000)
					} else {
						showToast(res.message || '登录失败')
					}
				} catch (error) {
					console.error('登录失败:', error)
					showToast('登录失败，请检查网络连接')
				} finally {
					this.loading = false
				}
			},
			
			// 游客登录
			guestLogin() {
				// 设置游客状态
				auth.setLogin('guest_token', { name: '游客用户', isGuest: true })
				showToast('已进入游客模式', 'success')
				
				setTimeout(() => {
					switchTab('/pages/index/index')
				}, 1000)
			},
			
			// 忘记密码
			goToForgotPassword() {
				navigateTo('/pages/forgot/forgot')
			},
			
			// 注册
			goToRegister() {
				navigateTo('/pages/register/register')
			}
		}
	}
</script>

<style scoped>
	.login-page {
		height: 100vh;
		position: relative;
		overflow: hidden;
	}
	
	/* 背景 */
	.background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1;
	}
	
	.bg-image {
		width: 100%;
		height: 100%;
	}
	
	.bg-overlay {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
	}
	
	/* 内容区域 */
	.content {
		position: relative;
		z-index: 2;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40rpx;
	}
	
	/* Logo区域 */
	.logo-section {
		text-align: center;
		margin-bottom: 80rpx;
	}
	
	.logo {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.app-name {
		display: block;
		font-size: 48rpx;
		font-weight: bold;
		color: white;
		margin-bottom: 10rpx;
	}
	
	.app-desc {
		display: block;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	/* 表单区域 */
	.form-section {
		flex: 1;
		display: flex;
		align-items: center;
	}
	
	.form-container {
		width: 100%;
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 30rpx;
		padding: 50rpx 40rpx;
		backdrop-filter: blur(10rpx);
	}
	
	/* Tab切换 */
	.tab-container {
		display: flex;
		background-color: #f8f8f8;
		border-radius: 25rpx;
		margin-bottom: 40rpx;
		overflow: hidden;
	}
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 25rpx 0;
		transition: all 0.3s;
	}
	
	.tab-item.active {
		background-color: #007aff;
	}
	
	.tab-text {
		font-size: 28rpx;
		color: #666;
		transition: color 0.3s;
	}
	
	.tab-item.active .tab-text {
		color: white;
		font-weight: bold;
	}
	
	/* 输入组 */
	.input-group {
		margin-bottom: 30rpx;
	}
	
	.input-item {
		display: flex;
		align-items: center;
		background-color: #f8f8f8;
		border-radius: 25rpx;
		padding: 0 30rpx;
		margin-bottom: 25rpx;
		height: 100rpx;
	}
	
	.input-icon {
		font-size: 32rpx;
		margin-right: 20rpx;
		width: 40rpx;
		text-align: center;
	}
	
	.input-field {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		background: transparent;
		border: none;
	}
	
	.input-toggle {
		font-size: 32rpx;
		color: #999;
		padding: 10rpx;
	}
	
	.code-btn {
		padding: 15rpx 25rpx;
		background-color: #007aff;
		color: white;
		border-radius: 20rpx;
		font-size: 24rpx;
		border: none;
	}
	
	.code-btn:disabled {
		background-color: #ccc;
	}
	
	/* 表单选项 */
	.form-options {
		text-align: right;
		margin-bottom: 40rpx;
	}
	
	.forgot-password {
		font-size: 26rpx;
		color: #007aff;
	}
	
	/* 登录按钮 */
	.login-btn {
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		border-radius: 50rpx;
		font-size: 32rpx;
		font-weight: bold;
		border: none;
		margin-bottom: 40rpx;
	}
	
	.login-btn:disabled {
		background: #ccc;
	}
	
	/* 其他选项 */
	.other-options {
		text-align: center;
	}
	
	.guest-btn {
		width: 100%;
		height: 80rpx;
		background-color: transparent;
		color: #666;
		border: 2rpx solid #ddd;
		border-radius: 40rpx;
		font-size: 28rpx;
		margin-bottom: 30rpx;
	}
	
	.register-link {
		font-size: 26rpx;
		color: #007aff;
	}
</style>
