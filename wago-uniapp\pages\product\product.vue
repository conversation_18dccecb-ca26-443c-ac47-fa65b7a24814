<template>
	<view class="product-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">产品库</view>
				<view class="search-icon" @click="goToSearch">
					<text class="iconfont icon-search">🔍</text>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 筛选栏 -->
			<view class="filter-bar">
				<view class="filter-item" @click="showTypeFilter">
					<text class="filter-text">{{ selectedType.name || '产品类型' }}</text>
					<text class="filter-arrow">▼</text>
				</view>
				<view class="filter-item" @click="showFunctionFilter">
					<text class="filter-text">{{ selectedFunction.name || '产品功能' }}</text>
					<text class="filter-arrow">▼</text>
				</view>
				<view class="filter-item" @click="showSeriesFilter">
					<text class="filter-text">{{ selectedSeries.name || '产品系列' }}</text>
					<text class="filter-arrow">▼</text>
				</view>
			</view>
			
			<!-- 产品列表 -->
			<scroll-view class="product-list" scroll-y @scrolltolower="loadMore" :refresher-enabled="true" 
				:refresher-triggered="refreshing" @refresherrefresh="onRefresh">
				<view class="product-item" v-for="(product, index) in products" :key="index" @click="goToProductDetail(product)">
					<view class="product-image-container">
						<image class="product-image" :src="product.productImg" mode="aspectFit"></image>
					</view>
					<view class="product-info">
						<text class="product-name">{{ product.productName }}</text>
						<text class="product-code">{{ product.productCode }}</text>
						<text class="product-desc">{{ product.productDesc }}</text>
						<view class="product-tags">
							<text class="tag" v-if="product.productType">{{ product.productType }}</text>
							<text class="tag" v-if="product.productSeries">{{ product.productSeries }}</text>
						</view>
					</view>
					<view class="product-actions">
						<text class="action-btn">查看详情</text>
					</view>
				</view>
				
				<!-- 加载更多 -->
				<view class="load-more" v-if="hasMore">
					<text v-if="loadingMore">加载中...</text>
					<text v-else>上拉加载更多</text>
				</view>
				
				<!-- 没有更多数据 -->
				<view class="no-more" v-if="!hasMore && products.length > 0">
					<text>没有更多数据了</text>
				</view>
				
				<!-- 空状态 -->
				<view class="empty-state" v-if="products.length === 0 && !loading">
					<text class="empty-text">暂无产品数据</text>
				</view>
			</scroll-view>
		</view>
		
		<!-- 筛选弹窗 -->
		<!-- 注意：这里需要安装uni-popup组件，暂时用view代替 -->
		<view class="filter-popup-mask" v-if="showFilterPopup" @click="closeFilter">
			<view class="filter-popup-content" @click.stop>
			<view class="filter-popup">
				<view class="popup-header">
					<text class="popup-title">{{ currentFilterTitle }}</text>
					<text class="popup-close" @click="closeFilter">✕</text>
				</view>
				<view class="filter-options">
					<view class="filter-option" 
						v-for="(option, index) in currentFilterOptions" 
						:key="index"
						:class="{ active: option.selected }"
						@click="selectFilterOption(option)">
						<text class="option-text">{{ option.name }}</text>
						<text class="option-check" v-if="option.selected">✓</text>
					</view>
				</view>
				<view class="popup-actions">
					<button class="btn-reset" @click="resetFilter">重置</button>
					<button class="btn-confirm" @click="confirmFilter">确定</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateTo, getStatusBarHeight } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				products: [],
				loading: false,
				refreshing: false,
				loadingMore: false,
				hasMore: true,
				page: 1,
				pageSize: 10,
				
				// 筛选相关
				selectedType: {},
				selectedFunction: {},
				selectedSeries: {},
				
				// 筛选选项
				typeOptions: [],
				functionOptions: [],
				seriesOptions: [],
				
				// 当前筛选弹窗
				showFilterPopup: false,
				currentFilterType: '',
				currentFilterTitle: '',
				currentFilterOptions: []
			}
		},
		
		async onLoad() {
			this.statusBarHeight = await getStatusBarHeight()
			this.loadFilterOptions()
			this.loadProducts()
		},
		
		methods: {
			// 加载筛选选项
			async loadFilterOptions() {
				try {
					// 这里应该调用相应的API获取筛选选项
					// 暂时使用模拟数据
					this.typeOptions = [
						{ id: '', name: '全部类型', selected: false },
						{ id: '1', name: 'I/O系统', selected: false },
						{ id: '2', name: '控制器', selected: false },
						{ id: '3', name: '接口模块', selected: false }
					]
					
					this.functionOptions = [
						{ id: '', name: '全部功能', selected: false },
						{ id: '1', name: '数字输入', selected: false },
						{ id: '2', name: '数字输出', selected: false },
						{ id: '3', name: '模拟输入', selected: false }
					]
					
					this.seriesOptions = [
						{ id: '', name: '全部系列', selected: false },
						{ id: '1', name: '750系列', selected: false },
						{ id: '2', name: '857系列', selected: false },
						{ id: '3', name: '885系列', selected: false }
					]
				} catch (error) {
					console.error('加载筛选选项失败:', error)
				}
			},
			
			// 加载产品列表
			async loadProducts(reset = false) {
				if (reset) {
					this.page = 1
					this.hasMore = true
					this.products = []
				}
				
				if (this.loading || this.loadingMore) return
				
				if (reset) {
					this.loading = true
				} else {
					this.loadingMore = true
				}
				
				try {
					const params = {
						page: this.page,
						pageSize: this.pageSize,
						productType: this.selectedType.id || '',
						productFunction: this.selectedFunction.id || '',
						productSeries: this.selectedSeries.id || ''
					}
					
					const res = await api.getProducts(params)
					
					if (res.code === 200) {
						const newProducts = res.data || []
						
						if (reset) {
							this.products = newProducts
						} else {
							this.products.push(...newProducts)
						}
						
						// 判断是否还有更多数据
						this.hasMore = newProducts.length === this.pageSize
						this.page++
					}
				} catch (error) {
					console.error('加载产品失败:', error)
					showToast('加载产品失败')
				} finally {
					this.loading = false
					this.loadingMore = false
					this.refreshing = false
				}
			},
			
			// 下拉刷新
			onRefresh() {
				this.refreshing = true
				this.loadProducts(true)
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loadingMore) {
					this.loadProducts()
				}
			},
			
			// 显示类型筛选
			showTypeFilter() {
				this.currentFilterType = 'type'
				this.currentFilterTitle = '选择产品类型'
				this.currentFilterOptions = [...this.typeOptions]
				this.showFilterPopup = true
			},
			
			// 显示功能筛选
			showFunctionFilter() {
				this.currentFilterType = 'function'
				this.currentFilterTitle = '选择产品功能'
				this.currentFilterOptions = [...this.functionOptions]
				this.showFilterPopup = true
			},
			
			// 显示系列筛选
			showSeriesFilter() {
				this.currentFilterType = 'series'
				this.currentFilterTitle = '选择产品系列'
				this.currentFilterOptions = [...this.seriesOptions]
				this.showFilterPopup = true
			},
			
			// 选择筛选选项
			selectFilterOption(option) {
				this.currentFilterOptions.forEach(item => {
					item.selected = item.id === option.id
				})
			},
			
			// 重置筛选
			resetFilter() {
				this.currentFilterOptions.forEach(item => {
					item.selected = item.id === ''
				})
			},
			
			// 确认筛选
			confirmFilter() {
				const selected = this.currentFilterOptions.find(item => item.selected) || this.currentFilterOptions[0]
				
				switch (this.currentFilterType) {
					case 'type':
						this.selectedType = selected
						this.typeOptions = [...this.currentFilterOptions]
						break
					case 'function':
						this.selectedFunction = selected
						this.functionOptions = [...this.currentFilterOptions]
						break
					case 'series':
						this.selectedSeries = selected
						this.seriesOptions = [...this.currentFilterOptions]
						break
				}
				
				this.closeFilter()
				this.loadProducts(true)
			},
			
			// 关闭筛选弹窗
			closeFilter() {
				this.showFilterPopup = false
			},
			
			// 跳转到搜索页面
			goToSearch() {
				navigateTo('/pages/search/search')
			},
			
			// 跳转到产品详情
			goToProductDetail(product) {
				navigateTo('/pages/detail/detail', {
					type: 'product',
					productId: product.productId,
					productName: product.productName
				})
			}
		}
	}
</script>

<style scoped>
	.product-page {
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 筛选栏 */
	.filter-bar {
		display: flex;
		background-color: white;
		padding: 20rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.filter-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 10rpx;
		border-right: 1rpx solid #f0f0f0;
	}
	
	.filter-item:last-child {
		border-right: none;
	}
	
	.filter-text {
		font-size: 28rpx;
		color: #333;
		margin-right: 10rpx;
	}
	
	.filter-arrow {
		font-size: 20rpx;
		color: #999;
	}
	
	/* 产品列表 */
	.product-list {
		height: calc(100vh - 200rpx);
		padding: 20rpx;
	}
	
	.product-item {
		display: flex;
		background-color: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.product-image-container {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.product-image {
		max-width: 100%;
		max-height: 100%;
	}
	
	.product-info {
		flex: 1;
		margin-right: 20rpx;
	}
	
	.product-name {
		display: block;
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		line-height: 1.4;
	}
	
	.product-code {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}
	
	.product-desc {
		display: block;
		font-size: 26rpx;
		color: #999;
		line-height: 1.4;
		margin-bottom: 15rpx;
	}
	
	.product-tags {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag {
		display: inline-block;
		padding: 5rpx 15rpx;
		background-color: #f0f0f0;
		color: #666;
		border-radius: 15rpx;
		font-size: 22rpx;
		margin-right: 10rpx;
		margin-bottom: 5rpx;
	}
	
	.product-actions {
		display: flex;
		align-items: center;
	}
	
	.action-btn {
		padding: 15rpx 25rpx;
		background-color: #007aff;
		color: white;
		border-radius: 25rpx;
		font-size: 24rpx;
	}
	
	/* 加载状态 */
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}
	
	/* 筛选弹窗 */
	.filter-popup-mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.5);
		z-index: 999;
		display: flex;
		align-items: flex-end;
	}

	.filter-popup-content {
		width: 100%;
		background-color: white;
		border-radius: 20rpx 20rpx 0 0;
		max-height: 80vh;
	}
	
	.popup-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.popup-close {
		font-size: 32rpx;
		color: #999;
		padding: 10rpx;
	}
	
	.filter-options {
		max-height: 60vh;
		overflow-y: auto;
	}
	
	.filter-option {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
		border-bottom: 1rpx solid #f8f8f8;
	}
	
	.filter-option.active {
		background-color: #f0f8ff;
	}
	
	.option-text {
		font-size: 30rpx;
		color: #333;
	}
	
	.filter-option.active .option-text {
		color: #007aff;
	}
	
	.option-check {
		font-size: 32rpx;
		color: #007aff;
	}
	
	.popup-actions {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
	}
	
	.btn-reset, .btn-confirm {
		flex: 1;
		padding: 25rpx;
		border-radius: 25rpx;
		text-align: center;
		font-size: 30rpx;
		border: none;
	}
	
	.btn-reset {
		background-color: #f8f8f8;
		color: #666;
	}
	
	.btn-confirm {
		background-color: #007aff;
		color: white;
	}
</style>
