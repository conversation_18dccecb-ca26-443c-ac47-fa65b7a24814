<template>
	<view class="search-page">
		<!-- 搜索栏 -->
		<view class="search-header" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="search-container">
				<view class="search-box">
					<text class="search-icon">🔍</text>
					<input class="search-input" 
						type="text" 
						placeholder="产品名称/产品长号" 
						v-model="keyword"
						@input="onInput"
						@confirm="onSearch"
						focus />
				</view>
				<text class="cancel-btn" @click="goBack">取消</text>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 搜索建议 -->
			<view class="search-suggestions" v-if="showSuggestions && suggestions.length > 0">
				<view class="suggestion-item" 
					v-for="(item, index) in suggestions" 
					:key="index"
					@click="selectSuggestion(item)">
					<text class="suggestion-icon">🔍</text>
					<text class="suggestion-text">{{ item }}</text>
				</view>
			</view>
			
			<!-- 搜索历史和热门搜索 -->
			<view class="search-default" v-if="!keyword && !showResults">
				<!-- 搜索历史 -->
				<view class="history-section" v-if="searchHistory.length > 0">
					<view class="section-header">
						<text class="section-title">搜索历史</text>
						<text class="clear-btn" @click="clearHistory">清除记录</text>
					</view>
					<view class="tag-list">
						<text class="tag-item" 
							v-for="(item, index) in searchHistory" 
							:key="index"
							@click="selectHistory(item)">
							{{ item }}
						</text>
					</view>
				</view>
				
				<!-- 热门搜索 -->
				<view class="hot-section">
					<view class="section-header">
						<text class="section-title">热门搜索</text>
					</view>
					<view class="tag-list">
						<text class="tag-item hot-tag" 
							v-for="(item, index) in hotSearches" 
							:key="index"
							@click="selectHot(item)">
							{{ item }}
						</text>
					</view>
				</view>
			</view>
			
			<!-- 搜索结果 -->
			<view class="search-results" v-if="showResults">
				<view class="result-header">
					<text class="result-count">找到 {{ totalCount }} 个相关产品</text>
				</view>
				
				<scroll-view class="result-list" scroll-y @scrolltolower="loadMore">
					<view class="product-item" 
						v-for="(product, index) in searchResults" 
						:key="index"
						@click="goToProductDetail(product)">
						<view class="product-image-container">
							<image class="product-image" :src="product.productImg" mode="aspectFit"></image>
						</view>
						<view class="product-info">
							<text class="product-name" v-html="highlightKeyword(product.productName)"></text>
							<text class="product-code">{{ product.productCode }}</text>
							<text class="product-desc">{{ product.productDesc }}</text>
							<view class="product-tags">
								<text class="tag" v-if="product.productType">{{ product.productType }}</text>
								<text class="tag" v-if="product.productSeries">{{ product.productSeries }}</text>
							</view>
						</view>
					</view>
					
					<!-- 加载更多 -->
					<view class="load-more" v-if="hasMore">
						<text v-if="loadingMore">加载中...</text>
						<text v-else>上拉加载更多</text>
					</view>
					
					<!-- 没有更多数据 -->
					<view class="no-more" v-if="!hasMore && searchResults.length > 0">
						<text>没有更多数据了</text>
					</view>
				</scroll-view>
				
				<!-- 空状态 */
				<view class="empty-state" v-if="searchResults.length === 0 && !loading">
					<image class="empty-icon" src="/static/icons/empty-search.png" mode="aspectFit"></image>
					<text class="empty-text">没有找到相关产品</text>
					<text class="empty-desc">试试其他关键词吧</text>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-overlay" v-if="loading">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">搜索中...</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { api } from '../../common/api.js'
	import { showToast, navigateTo, navigateBack, getStatusBarHeight, storage, debounce } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				keyword: '',
				showSuggestions: false,
				showResults: false,
				loading: false,
				loadingMore: false,
				hasMore: true,
				page: 1,
				pageSize: 10,
				totalCount: 0,
				
				// 搜索建议
				suggestions: [],
				
				// 搜索历史
				searchHistory: [],
				
				// 热门搜索
				hotSearches: [
					'750系列',
					'I/O模块',
					'控制器',
					'接线端子',
					'电源模块',
					'通讯模块',
					'857系列',
					'数字输入'
				],
				
				// 搜索结果
				searchResults: []
			}
		},
		
		async onLoad() {
			this.statusBarHeight = await getStatusBarHeight()
			this.loadSearchHistory()
		},
		
		methods: {
			// 输入事件
			onInput: debounce(function() {
				if (this.keyword.trim()) {
					this.getSuggestions()
				} else {
					this.showSuggestions = false
					this.showResults = false
				}
			}, 300),
			
			// 搜索
			async onSearch() {
				if (!this.keyword.trim()) {
					showToast('请输入搜索关键词')
					return
				}
				
				this.showSuggestions = false
				this.showResults = true
				this.saveSearchHistory(this.keyword.trim())
				this.searchProducts(true)
			},
			
			// 获取搜索建议
			async getSuggestions() {
				try {
					// 这里应该调用API获取搜索建议
					// 暂时使用模拟数据
					const mockSuggestions = [
						this.keyword + ' 系列',
						this.keyword + ' 模块',
						this.keyword + ' 控制器'
					].filter(item => item !== this.keyword)
					
					this.suggestions = mockSuggestions.slice(0, 5)
					this.showSuggestions = this.suggestions.length > 0
				} catch (error) {
					console.error('获取搜索建议失败:', error)
				}
			},
			
			// 搜索产品
			async searchProducts(reset = false) {
				if (reset) {
					this.page = 1
					this.hasMore = true
					this.searchResults = []
				}
				
				if (this.loading || this.loadingMore) return
				
				if (reset) {
					this.loading = true
				} else {
					this.loadingMore = true
				}
				
				try {
					const params = {
						keyword: this.keyword.trim(),
						page: this.page,
						pageSize: this.pageSize
					}
					
					const res = await api.searchProducts(params)
					
					if (res.code === 200) {
						const { list, total } = res.data
						
						if (reset) {
							this.searchResults = list || []
							this.totalCount = total || 0
						} else {
							this.searchResults.push(...(list || []))
						}
						
						this.hasMore = (list || []).length === this.pageSize
						this.page++
					}
				} catch (error) {
					console.error('搜索失败:', error)
					showToast('搜索失败')
				} finally {
					this.loading = false
					this.loadingMore = false
				}
			},
			
			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loadingMore) {
					this.searchProducts()
				}
			},
			
			// 选择搜索建议
			selectSuggestion(suggestion) {
				this.keyword = suggestion
				this.onSearch()
			},
			
			// 选择搜索历史
			selectHistory(history) {
				this.keyword = history
				this.onSearch()
			},
			
			// 选择热门搜索
			selectHot(hot) {
				this.keyword = hot
				this.onSearch()
			},
			
			// 保存搜索历史
			saveSearchHistory(keyword) {
				let history = this.searchHistory.filter(item => item !== keyword)
				history.unshift(keyword)
				history = history.slice(0, 10) // 最多保存10条
				
				this.searchHistory = history
				storage.set('searchHistory', history)
			},
			
			// 加载搜索历史
			loadSearchHistory() {
				this.searchHistory = storage.get('searchHistory', [])
			},
			
			// 清除搜索历史
			clearHistory() {
				this.searchHistory = []
				storage.remove('searchHistory')
			},
			
			// 高亮关键词
			highlightKeyword(text) {
				if (!this.keyword || !text) return text
				
				const regex = new RegExp(`(${this.keyword})`, 'gi')
				return text.replace(regex, '<span style="color: #007aff; font-weight: bold;">$1</span>')
			},
			
			// 跳转到产品详情
			goToProductDetail(product) {
				navigateTo('/pages/detail/detail', {
					type: 'product',
					productId: product.productId,
					productName: product.productName
				})
			},
			
			// 返回
			goBack() {
				navigateBack()
			}
		}
	}
</script>

<style scoped>
	.search-page {
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 搜索头部 */
	.search-header {
		background-color: white;
		border-bottom: 1rpx solid #f0f0f0;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 999;
	}
	
	.search-container {
		display: flex;
		align-items: center;
		padding: 20rpx;
		gap: 20rpx;
	}
	
	.search-box {
		flex: 1;
		display: flex;
		align-items: center;
		background-color: #f8f8f8;
		border-radius: 25rpx;
		padding: 0 30rpx;
		height: 70rpx;
	}
	
	.search-icon {
		font-size: 28rpx;
		color: #999;
		margin-right: 15rpx;
	}
	
	.search-input {
		flex: 1;
		font-size: 28rpx;
		color: #333;
		background: transparent;
		border: none;
	}
	
	.cancel-btn {
		font-size: 28rpx;
		color: #007aff;
		padding: 10rpx;
	}
	
	/* 页面内容 */
	.page-content {
		padding-top: 110rpx;
		height: 100vh;
		overflow: hidden;
	}
	
	/* 搜索建议 */
	.search-suggestions {
		background-color: white;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.suggestion-item {
		display: flex;
		align-items: center;
		padding: 30rpx 20rpx;
		border-bottom: 1rpx solid #f8f8f8;
	}
	
	.suggestion-item:last-child {
		border-bottom: none;
	}
	
	.suggestion-icon {
		font-size: 24rpx;
		color: #999;
		margin-right: 20rpx;
	}
	
	.suggestion-text {
		font-size: 28rpx;
		color: #333;
	}
	
	/* 默认页面 */
	.search-default {
		padding: 30rpx 20rpx;
	}
	
	.history-section, .hot-section {
		margin-bottom: 40rpx;
	}
	
	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 20rpx;
	}
	
	.section-title {
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
	}
	
	.clear-btn {
		font-size: 26rpx;
		color: #999;
	}
	
	.tag-list {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
	}
	
	.tag-item {
		display: inline-block;
		padding: 15rpx 25rpx;
		background-color: #f8f8f8;
		color: #666;
		border-radius: 25rpx;
		font-size: 26rpx;
	}
	
	.hot-tag {
		background-color: #fff2e6;
		color: #ff8c00;
	}
	
	/* 搜索结果 */
	.search-results {
		height: calc(100vh - 110rpx);
		display: flex;
		flex-direction: column;
	}
	
	.result-header {
		padding: 20rpx;
		background-color: white;
		border-bottom: 1rpx solid #f0f0f0;
	}
	
	.result-count {
		font-size: 26rpx;
		color: #666;
	}
	
	.result-list {
		flex: 1;
		padding: 20rpx;
	}
	
	.product-item {
		display: flex;
		background-color: white;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.product-image-container {
		width: 120rpx;
		height: 120rpx;
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
	
	.product-image {
		max-width: 100%;
		max-height: 100%;
	}
	
	.product-info {
		flex: 1;
	}
	
	.product-name {
		display: block;
		font-size: 30rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
		line-height: 1.4;
	}
	
	.product-code {
		display: block;
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;
	}
	
	.product-desc {
		display: block;
		font-size: 26rpx;
		color: #999;
		line-height: 1.4;
		margin-bottom: 15rpx;
	}
	
	.product-tags {
		display: flex;
		flex-wrap: wrap;
	}
	
	.tag {
		display: inline-block;
		padding: 5rpx 15rpx;
		background-color: #f0f0f0;
		color: #666;
		border-radius: 15rpx;
		font-size: 22rpx;
		margin-right: 10rpx;
		margin-bottom: 5rpx;
	}
	
	/* 加载状态 */
	.load-more, .no-more {
		text-align: center;
		padding: 30rpx;
		color: #999;
		font-size: 26rpx;
	}
	
	/* 空状态 */
	.empty-state {
		text-align: center;
		padding: 100rpx 20rpx;
	}
	
	.empty-icon {
		width: 200rpx;
		height: 200rpx;
		margin-bottom: 30rpx;
	}
	
	.empty-text {
		display: block;
		font-size: 32rpx;
		color: #666;
		margin-bottom: 15rpx;
	}
	
	.empty-desc {
		display: block;
		font-size: 26rpx;
		color: #999;
	}
	
	/* 加载覆盖层 */
	.loading-overlay {
		position: fixed;
		top: 110rpx;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(255, 255, 255, 0.8);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}
	
	.loading-content {
		text-align: center;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #007aff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
	}
</style>
