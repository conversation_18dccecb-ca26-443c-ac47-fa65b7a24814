<template>
	<view class="shop-page">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar" :style="{ paddingTop: statusBarHeight + 'px' }">
			<view class="navbar-content">
				<view class="navbar-title">{{ pageTitle }}</view>
				<view class="navbar-actions">
					<text class="action-btn" @click="goBack" v-if="canGoBack">←</text>
					<text class="action-btn" @click="goForward" v-if="canGoForward">→</text>
					<text class="action-btn" @click="refresh">⟳</text>
				</view>
			</view>
		</view>
		
		<!-- 页面内容 -->
		<view class="page-content">
			<!-- 加载进度条 -->
			<view class="progress-bar" v-if="loading">
				<view class="progress-fill" :style="{ width: progress + '%' }"></view>
			</view>
			
			<!-- WebView -->
			<web-view 
				class="webview" 
				:src="webUrl" 
				@message="onMessage"
				@load="onLoad"
				@error="onError">
			</web-view>
			
			<!-- 加载状态 -->
			<view class="loading-overlay" v-if="showLoading">
				<view class="loading-content">
					<view class="loading-spinner"></view>
					<text class="loading-text">加载中...</text>
				</view>
			</view>
			
			<!-- 错误状态 -->
			<view class="error-overlay" v-if="showError">
				<view class="error-content">
					<image class="error-icon" src="/static/icons/error.png" mode="aspectFit"></image>
					<text class="error-title">页面加载失败</text>
					<text class="error-desc">请检查网络连接后重试</text>
					<button class="retry-btn" @click="retry">重新加载</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getStatusBarHeight, showToast } from '../../common/utils.js'
	
	export default {
		data() {
			return {
				statusBarHeight: 0,
				webUrl: 'http://www.wago-mall.com',
				pageTitle: '在线商城',
				loading: false,
				showLoading: true,
				showError: false,
				progress: 0,
				canGoBack: false,
				canGoForward: false
			}
		},
		
		async onLoad() {
			this.statusBarHeight = await getStatusBarHeight()
		},
		
		onShow() {
			// 页面显示时可以刷新WebView状态
		},
		
		// 监听页面返回
		onBackPress() {
			if (this.canGoBack) {
				this.goBack()
				return true // 阻止默认返回行为
			}
			return false // 允许默认返回行为
		},
		
		methods: {
			// WebView加载完成
			onLoad(event) {
				console.log('WebView加载完成:', event)
				this.showLoading = false
				this.showError = false
				this.loading = false
				this.progress = 100
				
				// 更新页面标题
				if (event.detail && event.detail.title) {
					this.pageTitle = event.detail.title
				}
				
				// 更新导航状态
				this.updateNavigationState()
			},
			
			// WebView加载错误
			onError(event) {
				console.error('WebView加载错误:', event)
				this.showLoading = false
				this.showError = true
				this.loading = false
				showToast('页面加载失败')
			},
			
			// 接收WebView消息
			onMessage(event) {
				console.log('收到WebView消息:', event.detail.data)
				// 可以在这里处理来自WebView的消息
			},
			
			// 更新导航状态
			updateNavigationState() {
				// 注意：在某些平台上可能无法获取WebView的导航状态
				// 这里使用模拟状态
				this.canGoBack = true // 假设可以后退
				this.canGoForward = false // 假设不能前进
			},
			
			// 后退
			goBack() {
				// 在实际应用中，这里应该调用WebView的后退方法
				// 由于uni-app的web-view组件限制，这里只能模拟
				console.log('WebView后退')
				showToast('后退功能')
			},
			
			// 前进
			goForward() {
				// 在实际应用中，这里应该调用WebView的前进方法
				console.log('WebView前进')
				showToast('前进功能')
			},
			
			// 刷新
			refresh() {
				this.showLoading = true
				this.showError = false
				this.loading = true
				this.progress = 0
				
				// 重新加载WebView
				// 注意：uni-app的web-view组件可能不支持直接刷新
				// 可以通过改变src来实现刷新
				const timestamp = Date.now()
				this.webUrl = `http://www.wago-mall.com?t=${timestamp}`
			},
			
			// 重试
			retry() {
				this.refresh()
			}
		}
	}
</script>

<style scoped>
	.shop-page {
		height: 100vh;
		background-color: #f5f5f5;
	}
	
	/* 导航栏操作按钮 */
	.navbar-actions {
		display: flex;
		align-items: center;
		gap: 20rpx;
	}
	
	.action-btn {
		padding: 10rpx 15rpx;
		font-size: 32rpx;
		color: #333;
		border-radius: 8rpx;
		background-color: rgba(0, 0, 0, 0.05);
	}
	
	.action-btn:active {
		background-color: rgba(0, 0, 0, 0.1);
	}
	
	/* 进度条 */
	.progress-bar {
		height: 4rpx;
		background-color: #f0f0f0;
		position: relative;
		overflow: hidden;
	}
	
	.progress-fill {
		height: 100%;
		background-color: #007aff;
		transition: width 0.3s ease;
	}
	
	/* WebView */
	.webview {
		width: 100%;
		height: calc(100vh - 108rpx);
	}
	
	/* 加载覆盖层 */
	.loading-overlay {
		position: absolute;
		top: 108rpx;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}
	
	.loading-content {
		text-align: center;
	}
	
	.loading-spinner {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f0f0f0;
		border-top: 4rpx solid #007aff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin: 0 auto 20rpx;
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}
	
	.loading-text {
		font-size: 28rpx;
		color: #666;
	}
	
	/* 错误覆盖层 */
	.error-overlay {
		position: absolute;
		top: 108rpx;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: white;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 999;
	}
	
	.error-content {
		text-align: center;
		padding: 40rpx;
	}
	
	.error-icon {
		width: 120rpx;
		height: 120rpx;
		margin-bottom: 30rpx;
	}
	
	.error-title {
		display: block;
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
	}
	
	.error-desc {
		display: block;
		font-size: 26rpx;
		color: #666;
		margin-bottom: 40rpx;
		line-height: 1.5;
	}
	
	.retry-btn {
		padding: 25rpx 50rpx;
		background-color: #007aff;
		color: white;
		border-radius: 25rpx;
		font-size: 28rpx;
		border: none;
	}
	
	.retry-btn:active {
		background-color: #0056cc;
	}
</style>
